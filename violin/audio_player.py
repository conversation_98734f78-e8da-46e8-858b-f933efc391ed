#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的音频播放模块
用于播放音符对应的声音
"""

import math
import threading
import time
from typing import Optional

try:
    import numpy as np
    import pygame
    AUDIO_AVAILABLE = True
except ImportError:
    AUDIO_AVAILABLE = False
    print("警告: numpy 或 pygame 未安装，音频功能将被禁用")

class SimpleAudioPlayer:
    """简单的音频播放器"""
    
    def __init__(self, sample_rate: int = 44100, duration: float = 0.5):
        """
        初始化音频播放器
        
        Args:
            sample_rate: 采样率
            duration: 默认播放时长（秒）
        """
        self.sample_rate = sample_rate
        self.duration = duration
        self.is_initialized = False
        
        if AUDIO_AVAILABLE:
            self._initialize_pygame()
    
    def _initialize_pygame(self):
        """初始化pygame音频系统"""
        try:
            pygame.mixer.pre_init(
                frequency=self.sample_rate,
                size=-16,
                channels=2,
                buffer=512
            )
            pygame.mixer.init()
            self.is_initialized = True
            print("✅ 音频系统初始化成功")
        except Exception as e:
            print(f"❌ 音频系统初始化失败: {e}")
            self.is_initialized = False
    
    def generate_tone(self, frequency: float, duration: float = None) -> Optional[pygame.mixer.Sound]:
        """
        生成指定频率的音调
        
        Args:
            frequency: 频率（Hz）
            duration: 持续时间（秒）
            
        Returns:
            pygame.mixer.Sound对象，如果失败则返回None
        """
        if not self.is_initialized or not AUDIO_AVAILABLE:
            return None
        
        if duration is None:
            duration = self.duration
        
        try:
            # 生成时间数组
            frames = int(duration * self.sample_rate)
            arr = np.zeros((frames, 2))
            
            # 生成正弦波
            for i in range(frames):
                t = float(i) / self.sample_rate
                # 添加包络以避免爆音
                envelope = self._create_envelope(i, frames)
                wave = envelope * math.sin(2 * math.pi * frequency * t)
                arr[i][0] = wave  # 左声道
                arr[i][1] = wave  # 右声道
            
            # 转换为pygame可用的格式
            arr = (arr * 32767).astype(np.int16)
            sound = pygame.sndarray.make_sound(arr)
            return sound
            
        except Exception as e:
            print(f"生成音调失败: {e}")
            return None
    
    def _create_envelope(self, current_frame: int, total_frames: int) -> float:
        """
        创建音频包络以避免爆音
        
        Args:
            current_frame: 当前帧
            total_frames: 总帧数
            
        Returns:
            包络值
        """
        # 淡入时间（10ms）
        fade_in_frames = int(0.01 * self.sample_rate)
        # 淡出时间（50ms）
        fade_out_frames = int(0.05 * self.sample_rate)
        
        if current_frame < fade_in_frames:
            # 淡入
            return current_frame / fade_in_frames
        elif current_frame > total_frames - fade_out_frames:
            # 淡出
            return (total_frames - current_frame) / fade_out_frames
        else:
            # 稳定播放
            return 1.0
    
    def play_note_by_frequency(self, frequency: float, duration: float = None):
        """
        播放指定频率的音符
        
        Args:
            frequency: 频率（Hz）
            duration: 持续时间（秒）
        """
        if not self.is_initialized:
            return
        
        # 在新线程中播放音频，避免阻塞UI
        thread = threading.Thread(
            target=self._play_frequency_thread,
            args=(frequency, duration),
            daemon=True
        )
        thread.start()
    
    def play_note(self, note_info, duration: float = None):
        """
        播放音符对象
        
        Args:
            note_info: 音符信息对象（需要有frequency属性）
            duration: 持续时间（秒）
        """
        if hasattr(note_info, 'frequency'):
            self.play_note_by_frequency(note_info.frequency, duration)
        else:
            # 如果没有frequency属性，尝试根据音名计算频率
            frequency = self._calculate_frequency(note_info)
            if frequency:
                self.play_note_by_frequency(frequency, duration)
    
    def _play_frequency_thread(self, frequency: float, duration: float = None):
        """
        在线程中播放频率
        
        Args:
            frequency: 频率（Hz）
            duration: 持续时间（秒）
        """
        try:
            sound = self.generate_tone(frequency, duration)
            if sound:
                sound.play()
                # 等待播放完成
                if duration is None:
                    duration = self.duration
                time.sleep(duration)
        except Exception as e:
            print(f"播放音符失败: {e}")
    
    def _calculate_frequency(self, note_info) -> Optional[float]:
        """
        根据音符信息计算频率
        
        Args:
            note_info: 音符信息对象
            
        Returns:
            频率（Hz），如果计算失败则返回None
        """
        try:
            # 基础频率映射（A4 = 440Hz）
            note_frequencies = {
                'C': -9, 'C#': -8, 'D': -7, 'D#': -6, 'E': -5, 'F': -4,
                'F#': -3, 'G': -2, 'G#': -1, 'A': 0, 'A#': 1, 'B': 2
            }
            
            note_name = note_info.note_name
            octave = note_info.octave
            
            # 处理升号
            if hasattr(note_info, 'is_sharp') and note_info.is_sharp:
                note_name += '#'
            
            if note_name in note_frequencies:
                # 计算相对于A4的半音数
                semitones_from_a4 = note_frequencies[note_name] + (octave - 4) * 12
                # 计算频率
                frequency = 440.0 * (2 ** (semitones_from_a4 / 12))
                return frequency
            
        except Exception as e:
            print(f"计算频率失败: {e}")
        
        return None

# 全局音频播放器实例
audio_player = SimpleAudioPlayer()

def play_note(note_info, duration: float = 0.5):
    """
    播放音符的便捷函数
    
    Args:
        note_info: 音符信息对象
        duration: 持续时间（秒）
    """
    audio_player.play_note(note_info, duration)

def play_frequency(frequency: float, duration: float = 0.5):
    """
    播放指定频率的便捷函数
    
    Args:
        frequency: 频率（Hz）
        duration: 持续时间（秒）
    """
    audio_player.play_note_by_frequency(frequency, duration)
