#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五线谱视奏练习程序主应用类 - 集成小提琴指板
完全保留原有钢琴键盘，只添加小提琴指板到右侧
"""

import tkinter as tk
from tkinter import ttk, Canvas, Frame, Button, Label
import random
from typing import Optional

# 导入小提琴指板模块
from staff_practice_violin_fingerboard import ViolinFingerboard, ViolinPosition, violin_fingerboard_data

# 导入音频播放模块
try:
    from audio_player import audio_player
    AUDIO_ENABLED = True
except ImportError:
    AUDIO_ENABLED = False
    print("警告: 音频模块导入失败，音频功能将被禁用")

# 模拟原有的音符模型和数据库（保持与原系统兼容）
class NoteInfo:
    """音符信息类 - 与原系统兼容"""
    def __init__(self, pitch: str, note_name: str, octave: int, frequency: float = None):
        self.pitch = pitch
        self.note_name = note_name
        self.octave = octave
        self.frequency = frequency if frequency is not None else self._calculate_frequency(note_name, octave)
        self.solfege = self._get_solfege(note_name)
        self.is_black_key = '#' in pitch

    def _get_solfege(self, note_name: str) -> str:
        """获取唱名"""
        solfege_map = {
            'C': 'do', 'D': 're', 'E': 'mi', 'F': 'fa',
            'G': 'sol', 'A': 'la', 'B': 'si'
        }
        return solfege_map.get(note_name, '')

    def _calculate_frequency(self, note_name: str, octave: int) -> float:
        """计算音符频率"""
        # 基础频率映射（A4 = 440Hz）
        note_frequencies = {
            'C': -9, 'D': -7, 'E': -5, 'F': -4,
            'G': -2, 'A': 0, 'B': 2
        }

        if note_name in note_frequencies:
            # 计算相对于A4的半音数
            semitones_from_a4 = note_frequencies[note_name] + (octave - 4) * 12
            # 计算频率
            frequency = 440.0 * (2 ** (semitones_from_a4 / 12))
            return frequency

        return 440.0  # 默认频率

class NoteDatabase:
    """音符数据库 - 模拟原有的note_db"""
    
    def __init__(self):
        self.notes = {}
        self.white_keys = []
        self.black_keys = []
        self._initialize_notes()
    
    def _initialize_notes(self):
        """初始化音符数据 - 恢复原有范围A3-C6，修正前三个音"""
        # 完整的音符序列：A3到C6
        # 修正前三个音的问题
        notes_sequence = [
            # 正确的前三个音
            ('A3', 'A', 3, False),  # 第1个白键
            ('A#3', 'A', 3, True),  # 第1个黑键
            ('B3', 'B', 3, False),  # 第2个白键

            # 从C4开始的正常序列
            ('C4', 'C', 4, False),
            ('C#4', 'C', 4, True),
            ('D4', 'D', 4, False),
            ('D#4', 'D', 4, True),
            ('E4', 'E', 4, False),
            ('F4', 'F', 4, False),
            ('F#4', 'F', 4, True),
            ('G4', 'G', 4, False),
            ('G#4', 'G', 4, True),
            ('A4', 'A', 4, False),
            ('A#4', 'A', 4, True),
            ('B4', 'B', 4, False),
            ('C5', 'C', 5, False),
            ('C#5', 'C', 5, True),
            ('D5', 'D', 5, False),
            ('D#5', 'D', 5, True),
            ('E5', 'E', 5, False),
            ('F5', 'F', 5, False),
            ('F#5', 'F', 5, True),
            ('G5', 'G', 5, False),
            ('G#5', 'G', 5, True),
            ('A5', 'A', 5, False),
            ('A#5', 'A', 5, True),
            ('B5', 'B', 5, False),
            ('C6', 'C', 6, False),
        ]

        # 基础频率（A3 = 220 Hz）
        a3_freq = 220.0

        for i, (pitch, note_name, octave, is_black) in enumerate(notes_sequence):
            # 计算频率（每个半音步长为2^(1/12)）
            frequency = a3_freq * (2 ** (i / 12))

            note = NoteInfo(pitch, note_name, octave, frequency)
            self.notes[pitch] = note

            if is_black:
                self.black_keys.append(note)
            else:
                self.white_keys.append(note)
    
    def get_note(self, pitch: str) -> Optional[NoteInfo]:
        """根据音高获取音符"""
        return self.notes.get(pitch)
    
    def get_white_keys(self):
        """获取所有白键"""
        return self.white_keys
    
    def get_black_keys(self):
        """获取所有黑键"""
        return self.black_keys
    
    def get_random_white_key(self) -> NoteInfo:
        """随机获取一个白键音符"""
        # 从小提琴指板数据中选择
        all_positions = violin_fingerboard_data.get_all_positions()
        non_open_positions = [pos for pos in all_positions if pos.fret > 0]
        
        if non_open_positions:
            pos = random.choice(non_open_positions)
            return NoteInfo(pos.pitch, pos.note_name, pos.octave)
        
        return random.choice(self.white_keys) if self.white_keys else None

# 创建全局音符数据库实例
note_db = NoteDatabase()

# 原有的钢琴键盘类 - 完全保持原样
class PianoKeyboard:
    """虚拟钢琴键盘类 - 原有实现"""
    
    def __init__(self, canvas: Canvas, width: int = 600, height: int = 120):
        """
        初始化钢琴键盘
        
        Args:
            canvas: Tkinter Canvas对象
            width: 键盘宽度
            height: 键盘高度
        """
        self.canvas = canvas
        self.width = width
        self.height = height
        
        # 键盘参数
        self.keyboard_left = 20
        self.keyboard_right = width - 20
        self.white_key_height = 80
        self.black_key_height = 50
        
        # 白键和黑键的信息
        self.white_keys = note_db.get_white_keys()
        self.black_keys = note_db.get_black_keys()
        
        # 键位映射：音符 -> (x, y, width, height, is_black)
        self.key_positions = {}
        
        # 按下的键
        self.pressed_keys = set()
        
        # 点击回调函数
        self.on_key_click = None
        
        self._calculate_key_positions()
        self._draw_keyboard()
        self._bind_events()
    
    def _calculate_key_positions(self):
        """计算所有键的位置"""
        # 计算白键宽度
        white_key_count = len(self.white_keys)
        available_width = self.keyboard_right - self.keyboard_left
        white_key_width = available_width // white_key_count
        
        # 计算白键位置
        for i, note in enumerate(self.white_keys):
            x = self.keyboard_left + i * white_key_width
            y = self.height - self.white_key_height
            self.key_positions[note.pitch] = (x, y, white_key_width, self.white_key_height, False)
        
        # 计算黑键位置
        black_key_width = white_key_width * 0.6
        
        for note in self.black_keys:
            # 根据音符名称确定黑键位置
            note_name = note.note_name
            octave = note.octave
            
            # 找到对应的白键位置
            white_key_index = self._get_white_key_index_for_black_key(note)
            if white_key_index is not None:
                white_key_x = self.keyboard_left + white_key_index * white_key_width
                
                # 黑键位置偏移
                if note_name in ['A', 'C', 'F']:  # A#, C#, F# 在右侧
                    x = white_key_x + white_key_width - black_key_width // 2
                else:  # D#, G# 在左侧
                    x = white_key_x - black_key_width // 2
                
                y = self.height - self.white_key_height
                self.key_positions[note.pitch] = (int(x), y, int(black_key_width), self.black_key_height, True)
    
    def _get_white_key_index_for_black_key(self, black_note):
        """获取黑键对应的白键索引"""
        note_name = black_note.note_name
        octave = black_note.octave
        
        # 黑键与白键的对应关系
        if note_name == 'A':  # A#
            white_pitch = f'A{octave}'
        elif note_name == 'C':  # C#
            white_pitch = f'C{octave}'
        elif note_name == 'D':  # D#
            white_pitch = f'D{octave}'
        elif note_name == 'F':  # F#
            white_pitch = f'F{octave}'
        elif note_name == 'G':  # G#
            white_pitch = f'G{octave}'
        else:
            return None
        
        # 找到白键在列表中的索引
        for i, white_note in enumerate(self.white_keys):
            if white_note.pitch == white_pitch:
                return i
        
        return None
    
    def _draw_keyboard(self):
        """绘制键盘"""
        # 清空画布
        self.canvas.delete("all")
        
        # 先绘制白键
        for note in self.white_keys:
            if note.pitch in self.key_positions:
                x, y, width, height, _ = self.key_positions[note.pitch]
                
                # 确定颜色
                fill_color = "#f0f0f0" if note.pitch in self.pressed_keys else "white"
                
                # 绘制白键
                self.canvas.create_rectangle(
                    x, y, x + width, y + height,
                    fill=fill_color, outline="black", width=1,
                    tags=f"key_{note.pitch}"
                )
                
                # 绘制音符标签
                self.canvas.create_text(
                    x + width // 2, y + height - 15,
                    text=f"{note.note_name}{note.octave}",
                    font=("Arial", 8), fill="black",
                    tags=f"label_{note.pitch}"
                )
        
        # 再绘制黑键（覆盖在白键上方）
        for note in self.black_keys:
            if note.pitch in self.key_positions:
                x, y, width, height, _ = self.key_positions[note.pitch]
                
                # 确定颜色
                fill_color = "#404040" if note.pitch in self.pressed_keys else "black"
                
                # 绘制黑键
                self.canvas.create_rectangle(
                    x, y, x + width, y + height,
                    fill=fill_color, outline="gray", width=1,
                    tags=f"key_{note.pitch}"
                )
                
                # 绘制音符标签
                self.canvas.create_text(
                    x + width // 2, y + height - 10,
                    text=f"{note.note_name}#{note.octave}",
                    font=("Arial", 7), fill="white",
                    tags=f"label_{note.pitch}"
                )
    
    def _bind_events(self):
        """绑定鼠标事件"""
        self.canvas.bind("<Button-1>", self._on_click)
        self.canvas.bind("<ButtonRelease-1>", self._on_release)
    
    def _on_click(self, event):
        """处理鼠标点击事件"""
        clicked_note = self._get_note_at_position(event.x, event.y)
        if clicked_note:
            self.press_key(clicked_note.pitch)
            if self.on_key_click:
                self.on_key_click(clicked_note)
    
    def _on_release(self, event):
        """处理鼠标释放事件"""
        # 释放所有按键
        self.release_all_keys()
    
    def _get_note_at_position(self, x, y):
        """根据坐标获取对应的音符"""
        # 先检查黑键（优先级更高）
        for note in self.black_keys:
            if note.pitch in self.key_positions:
                key_x, key_y, width, height, _ = self.key_positions[note.pitch]
                if (key_x <= x <= key_x + width and 
                    key_y <= y <= key_y + height):
                    return note
        
        # 再检查白键
        for note in self.white_keys:
            if note.pitch in self.key_positions:
                key_x, key_y, width, height, _ = self.key_positions[note.pitch]
                if (key_x <= x <= key_x + width and 
                    key_y <= y <= key_y + height):
                    return note
        
        return None
    
    def press_key(self, pitch):
        """按下指定的键"""
        self.pressed_keys.add(pitch)
        self._redraw_key(pitch)
    
    def release_key(self, pitch):
        """释放指定的键"""
        self.pressed_keys.discard(pitch)
        self._redraw_key(pitch)
    
    def release_all_keys(self):
        """释放所有按键"""
        self.pressed_keys.clear()
        self._draw_keyboard()
    
    def _redraw_key(self, pitch):
        """重新绘制指定的键"""
        if pitch not in self.key_positions:
            return
        
        note = note_db.get_note(pitch)
        if not note:
            return
        
        x, y, width, height, is_black = self.key_positions[pitch]
        
        # 删除旧的键
        self.canvas.delete(f"key_{pitch}")
        self.canvas.delete(f"label_{pitch}")
        
        # 确定颜色
        if is_black:
            fill_color = "#404040" if pitch in self.pressed_keys else "black"
            text_color = "white"
        else:
            fill_color = "#f0f0f0" if pitch in self.pressed_keys else "white"
            text_color = "black"
        
        # 重新绘制键
        self.canvas.create_rectangle(
            x, y, x + width, y + height,
            fill=fill_color, outline="black" if not is_black else "gray", width=1,
            tags=f"key_{pitch}"
        )
        
        # 重新绘制标签
        font_size = 7 if is_black else 8
        label_y = y + height - (10 if is_black else 15)
        self.canvas.create_text(
            x + width // 2, label_y,
            text=f"{note.note_name}{'#' if is_black else ''}{note.octave}",
            font=("Arial", font_size), fill=text_color,
            tags=f"label_{pitch}"
        )
    
    def set_key_click_callback(self, callback):
        """设置按键点击回调函数"""
        self.on_key_click = callback

# 原有的五线谱渲染器类 - 保持原样
class StaffRenderer:
    """五线谱渲染器类"""

    def __init__(self, canvas: Canvas, width: int = 500, height: int = 200):
        self.canvas = canvas
        self.width = width
        self.height = height
        self.staff_left = 80
        self.staff_right = width - 50
        self.staff_top = 50
        self.line_spacing = 15
        self.middle_line_y = self.staff_top + 2 * self.line_spacing
        self.current_notes = []
        self._draw_staff()

    def _draw_staff(self):
        """绘制五线谱"""
        self.canvas.delete("all")

        # 绘制五条线
        for i in range(5):
            y = self.staff_top + i * self.line_spacing
            self.canvas.create_line(
                self.staff_left, y, self.staff_right, y,
                fill="black", width=1
            )

        # 绘制高音谱号
        self.canvas.create_text(
            self.staff_left + 20, self.middle_line_y,
            text="𝄞", font=("Arial", 40), fill="black"
        )

    def add_note(self, note_info):
        """添加音符到五线谱"""
        self.clear_notes()
        self.current_notes = [note_info]

        # 音符位置映射 - 精确的位置，确保音符在线上或间中居中
        # 使用精确的staff_y值，以半间距为单位
        # 第三线（B4）为基准0，向上为正值，向下为负值
        # 偶数值表示在线上，奇数值表示在间中
        staff_y_positions = {
            # 下加线区域
            'A3': -8,    # 下加二线（线上）
            'A#3': -7,   # 下加一线和下加二线之间（间中）
            'B3': -7,    # 下加一线和下加二线之间（间中）
            'C4': -6,    # 下加一线（线上）
            'C#4': -6,   # 下加一线（线上）
            'D4': -5,    # 下加一线和第一线之间（间中）
            'D#4': -5,   # 下加一线和第一线之间（间中）

            # 五线谱主体
            'E4': -4,    # 第一线（线上）
            'F4': -3,    # 第一间（间中）
            'F#4': -3,   # 第一间（间中）
            'G4': -2,    # 第二线（线上）
            'G#4': -2,   # 第二线（线上）
            'A4': -1,    # 第二间（间中）
            'A#4': -1,   # 第二间（间中）
            'B4': 0,     # 第三线（基准线，线上）
            'C5': 1,     # 第三间（间中）
            'C#5': 1,    # 第三间（间中）
            'D5': 2,     # 第四线（线上）
            'D#5': 2,    # 第四线（线上）
            'E5': 3,     # 第四间（间中）
            'F5': 4,     # 第五线（线上）
            'F#5': 4,    # 第五线（线上）

            # 上加线区域
            'G5': 5,     # 第五线上方（间中）
            'G#5': 5,    # 第五线上方（间中）
            'A5': 6,     # 上加一线（线上）
            'A#5': 6,    # 上加一线（线上）
            'B5': 7,     # 上加一线和上加二线之间（间中）
            'C6': 8,     # 上加二线（线上）
        }

        # 使用精确的半间距计算：确保音符位置精确
        staff_y = staff_y_positions.get(note_info.pitch, 0)
        half_spacing = self.line_spacing / 2.0  # 使用浮点数除法，确保精度
        note_y = int(self.middle_line_y - staff_y * half_spacing)
        note_x = self.staff_left + 120

        # 绘制加线（如果需要）
        self._draw_ledger_lines(note_x, note_y)

        # 绘制音符
        self.canvas.create_oval(
            note_x - 8, note_y - 6,
            note_x + 8, note_y + 6,
            fill="black", outline="black",
            tags="note"
        )

        # 绘制符干
        if note_y > self.middle_line_y:
            # 符干向上
            self.canvas.create_line(
                note_x + 8, note_y, note_x + 8, note_y - 30,
                fill="black", width=2,
                tags="note"
            )
        else:
            # 符干向下
            self.canvas.create_line(
                note_x - 8, note_y, note_x - 8, note_y + 30,
                fill="black", width=2,
                tags="note"
            )

    def _draw_ledger_lines(self, x, y):
        """绘制加线"""
        # 上加线
        if y < self.staff_top:
            line_y = self.staff_top - self.line_spacing
            while line_y >= y - 5:
                self.canvas.create_line(
                    x - 12, line_y, x + 12, line_y,
                    fill="black", width=1, tags="note"
                )
                line_y -= self.line_spacing

        # 下加线
        if y > self.staff_top + 4 * self.line_spacing:
            line_y = self.staff_top + 5 * self.line_spacing
            while line_y <= y + 5:
                self.canvas.create_line(
                    x - 12, line_y, x + 12, line_y,
                    fill="black", width=1, tags="note"
                )
                line_y += self.line_spacing

    def clear_notes(self):
        """清除音符"""
        self.canvas.delete("note")
        self.current_notes = []

class StaffPracticeApp:
    """五线谱视奏练习应用程序 - 集成小提琴指板"""

    def __init__(self, root: tk.Tk):
        self.root = root
        self.setup_window()

        # 当前模式：'sight_reading' 或 'piano_keyboard'
        self.current_mode = 'sight_reading'

        # 当前显示的音符
        self.current_note = None

        # 是否显示答案
        self.show_answer = False

        # 计分系统
        self.score = 0

        # 创建UI组件
        self.create_widgets()
        self.setup_layout()
        self.bind_events()

        # 开始第一个练习
        self.next_note()

    def setup_window(self):
        """设置窗口属性"""
        self.root.title("五线谱视奏练习 - 集成小提琴指板")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)

    def create_widgets(self):
        """创建所有UI组件"""
        # 主框架
        self.main_frame = Frame(self.root)

        # 标题
        self.title_label = Label(
            self.main_frame,
            text="五线谱视奏练习",
            font=("Arial", 16, "bold")
        )

        # 模式选择框架
        self.mode_frame = Frame(self.main_frame)
        self.mode_var = tk.StringVar(value="sight_reading")

        self.sight_reading_radio = ttk.Radiobutton(
            self.mode_frame,
            text="视奏练习",
            variable=self.mode_var,
            value="sight_reading",
            command=self.on_mode_change
        )

        self.piano_keyboard_radio = ttk.Radiobutton(
            self.mode_frame,
            text="钢琴键盘",
            variable=self.mode_var,
            value="piano_keyboard",
            command=self.on_mode_change
        )

        # 主要内容框架（左右分栏）
        self.content_frame = Frame(self.main_frame)

        # 左侧框架（原有内容）
        self.left_frame = Frame(self.content_frame)

        # 五线谱画布
        self.staff_canvas = Canvas(
            self.left_frame,
            width=500, height=200,
            bg="white", relief="sunken", borderwidth=2
        )
        self.staff_renderer = StaffRenderer(self.staff_canvas, 500, 200)

        # 音名唱名显示框架
        self.note_info_frame = Frame(self.left_frame)

        # 音名显示
        self.note_name_label = Label(
            self.note_info_frame,
            text="音名: A B C D E F G",
            font=("Arial", 12)
        )

        # 唱名显示
        self.solfege_label = Label(
            self.note_info_frame,
            text="唱名: la si do re mi fa sol",
            font=("Arial", 12)
        )

        # 计分显示
        self.score_label = Label(
            self.note_info_frame,
            text="得分: 0",
            font=("Arial", 12, "bold"),
            fg="blue"
        )

        # 控制按钮框架
        self.control_frame = Frame(self.left_frame)

        self.next_button = Button(
            self.control_frame,
            text="下一题",
            command=self.next_note,
            font=("Arial", 10)
        )

        self.answer_button = Button(
            self.control_frame,
            text="显示答案",
            command=self.toggle_answer,
            font=("Arial", 10)
        )

        self.clear_button = Button(
            self.control_frame,
            text="清除五线谱",
            command=self.clear_staff,
            font=("Arial", 10)
        )

        # 钢琴键盘画布 - 使用原有尺寸和设置
        self.piano_canvas = Canvas(
            self.left_frame,
            width=750,
            height=120,
            bg="lightgray",
            relief="sunken",
            borderwidth=2
        )

        # 钢琴键盘 - 使用原有的PianoKeyboard类
        self.piano_keyboard = PianoKeyboard(self.piano_canvas, 750, 120)
        self.piano_keyboard.set_key_click_callback(self.on_piano_key_click)

        # 右侧框架（小提琴指板）
        self.right_frame = Frame(self.content_frame)

        # 小提琴指板画布
        self.violin_canvas = Canvas(
            self.right_frame,
            width=350, height=500,
            bg="lightyellow", relief="sunken", borderwidth=2
        )
        self.violin_fingerboard = ViolinFingerboard(self.violin_canvas, 350, 500)
        self.violin_fingerboard.set_position_click_callback(self.on_violin_position_click)

    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 标题
        self.title_label.pack(pady=(0, 10))

        # 模式选择
        self.mode_frame.pack(pady=(0, 10))
        self.sight_reading_radio.pack(side="left", padx=(0, 20))
        self.piano_keyboard_radio.pack(side="left")

        # 主要内容（左右分栏）
        self.content_frame.pack(fill="both", expand=True, pady=(0, 10))

        # 左侧内容
        self.left_frame.pack(side="left", fill="both", expand=True, padx=(0, 20))

        # 五线谱
        Label(self.left_frame, text="五线谱", font=("Arial", 12, "bold")).pack()
        self.staff_canvas.pack(pady=(5, 10))

        # 音名唱名信息
        self.note_info_frame.pack(pady=(0, 10))
        self.note_name_label.pack()
        self.solfege_label.pack()
        # 计分标签在视奏模式下显示
        if self.current_mode == "sight_reading":
            self.score_label.pack(pady=(5, 0))

        # 控制按钮
        self.control_frame.pack(pady=(0, 10))
        self.next_button.pack(side="left", padx=(0, 10))
        self.answer_button.pack(side="left", padx=(0, 10))
        self.clear_button.pack(side="left")

        # 钢琴键盘
        Label(self.left_frame, text="钢琴键盘", font=("Arial", 12, "bold")).pack(pady=(10, 5))
        self.piano_canvas.pack()

        # 右侧内容（小提琴指板）
        self.right_frame.pack(side="right", fill="y")
        self.violin_canvas.pack()

    def bind_events(self):
        """绑定事件"""
        self.root.bind("<KeyPress-space>", lambda e: self.next_note())
        self.root.bind("<KeyPress-a>", lambda e: self.toggle_answer())
        self.root.bind("<KeyPress-c>", lambda e: self.clear_staff())

    def on_mode_change(self):
        """模式切换处理"""
        self.current_mode = self.mode_var.get()

        if self.current_mode == "sight_reading":
            # 视奏练习模式
            self.next_button.config(state="normal")
            self.answer_button.config(state="normal")
            self.clear_button.config(state="disabled")
            self.score_label.pack(pady=(5, 0))  # 显示计分
            # 开始新的练习
            self.next_note()
        else:
            # 钢琴键盘模式
            self.next_button.config(state="disabled")
            self.answer_button.config(state="disabled")
            self.clear_button.config(state="normal")
            self.score_label.pack_forget()  # 隐藏计分
            self.clear_staff()

    def next_note(self):
        """显示下一个随机音符"""
        # 随机选择一个音符
        self.current_note = note_db.get_random_white_key()

        # 清除五线谱并显示新音符
        self.staff_renderer.clear_notes()
        self.staff_renderer.add_note(self.current_note)

        # 清除指板高亮
        self.violin_fingerboard.clear_highlight()

        # 隐藏答案
        self.show_answer = False
        self.answer_button.config(text="显示答案")
        self.update_note_info_display()

        # 在视奏练习模式下播放音符
        if self.current_mode == "sight_reading" and AUDIO_ENABLED:
            try:
                audio_player.play_note(self.current_note, duration=0.8)
            except Exception as e:
                print(f"播放音符失败: {e}")

    def toggle_answer(self):
        """切换答案显示"""
        if not self.current_note:
            return

        self.show_answer = not self.show_answer
        self.answer_button.config(text="隐藏答案" if self.show_answer else "显示答案")
        self.update_note_info_display()

        if self.show_answer:
            # 高亮小提琴指板上的对应位置
            matches = violin_fingerboard_data.find_positions_by_note(
                self.current_note.note_name, self.current_note.octave
            )
            if matches:
                # 高亮第一个匹配的位置
                pos = matches[0]
                self.violin_fingerboard.highlight_position(pos.string, pos.fret)
        else:
            self.violin_fingerboard.clear_highlight()

    def on_piano_key_click(self, note: NoteInfo):
        """钢琴键点击处理 - 添加小提琴指板联动"""
        if self.current_mode == "piano_keyboard":
            # 钢琴键盘模式：在五线谱上显示音符
            self.staff_renderer.add_note(note)

            # 更新当前音符信息
            self.current_note = note
            self.show_answer = True
            self.update_note_info_display()

            # 联动：高亮小提琴指板对应位置
            self._highlight_violin_for_note(note)

        elif self.current_mode == "sight_reading" and self.current_note:
            # 视奏模式：检查答案并计分
            self.check_answer(note)

            # 联动：如果答案正确，同时高亮小提琴指板
            if (note.note_name == self.current_note.note_name and
                note.octave == self.current_note.octave):
                self._highlight_violin_for_note(note)

    def check_answer(self, clicked_note: NoteInfo):
        """检查答案并更新分数"""
        if not self.current_note:
            return

        # 比较音符的音名和八度
        if (clicked_note.note_name == self.current_note.note_name and
            clicked_note.octave == self.current_note.octave):
            # 正确答案
            self.score += 1
            self.update_score_display()

            # 生成新的随机音符
            self.next_note()
        else:
            # 错误答案
            self.score -= 1
            self.update_score_display()

            # 音符不变，继续显示当前音符

    def on_violin_position_click(self, position: ViolinPosition):
        """处理小提琴指板位置点击 - 添加钢琴键盘联动"""
        if self.current_mode == "sight_reading":
            # 视奏模式：检查答案
            if not self.current_note:
                return

            if (position.note_name == self.current_note.note_name and
                position.octave == self.current_note.octave):
                # 正确答案
                self.score += 1
                self.violin_fingerboard.highlight_position(position.string, position.fret)
                self.update_score_display()

                # 联动：高亮钢琴键盘对应键
                self._highlight_piano_for_position(position)

                # 延迟生成下一题
                self.root.after(1000, self.next_note)
            else:
                # 错误答案
                self.score -= 1
                self.update_score_display()

        elif self.current_mode == "piano_keyboard":
            # 钢琴键盘模式：显示音符并联动
            # 创建对应的音符信息
            note = NoteInfo(position.pitch, position.note_name, position.octave)

            # 在五线谱上显示音符
            self.staff_renderer.add_note(note)

            # 更新当前音符信息
            self.current_note = note
            self.show_answer = True
            self.update_note_info_display()

            # 高亮小提琴指板位置
            self.violin_fingerboard.highlight_position(position.string, position.fret)

            # 联动：高亮钢琴键盘对应键
            self._highlight_piano_for_position(position)

    def _highlight_violin_for_note(self, note: NoteInfo):
        """为音符高亮对应的小提琴指板位置"""
        # 查找小提琴指板上对应的位置
        matches = violin_fingerboard_data.find_positions_by_note(
            note.note_name, note.octave
        )

        if matches:
            # 高亮第一个匹配的位置
            pos = matches[0]
            self.violin_fingerboard.highlight_position(pos.string, pos.fret)

            # 延迟清除高亮
            self.root.after(1500, self.violin_fingerboard.clear_highlight)

    def _highlight_piano_for_position(self, position: ViolinPosition):
        """为小提琴位置高亮对应的钢琴键"""
        # 高亮钢琴键盘上对应的键
        self.piano_keyboard.press_key(position.pitch)

        # 延迟释放按键
        self.root.after(1500, lambda: self.piano_keyboard.release_key(position.pitch))

    def update_note_info_display(self):
        """更新音名唱名显示 - 保持原有逻辑"""
        if not self.current_note or not self.show_answer:
            # 显示所有音名和唱名 - 修正对应关系
            self.note_name_label.config(text="音名: C D E F G A B")
            self.solfege_label.config(text="唱名: do re mi fa sol la si")
        else:
            # 高亮显示当前音符 - 修正音名和唱名的对应顺序
            note_names = ["C", "D", "E", "F", "G", "A", "B"]
            solfeges = ["do", "re", "mi", "fa", "sol", "la", "si"]

            # 构建高亮显示的文本
            note_text = "音名: "
            solfege_text = "唱名: "

            for name in note_names:
                if name == self.current_note.note_name:
                    note_text += f"[{name}] "
                else:
                    note_text += f"{name} "

            for solfege in solfeges:
                if solfege == self.current_note.solfege:
                    solfege_text += f"[{solfege}] "
                else:
                    solfege_text += f"{solfege} "

            self.note_name_label.config(text=note_text.strip())
            self.solfege_label.config(text=solfege_text.strip())

    def update_score_display(self):
        """更新分数显示"""
        self.score_label.config(text=f"得分: {self.score}")

        # 根据分数改变颜色
        if self.score > 0:
            self.score_label.config(fg="green")
        elif self.score < 0:
            self.score_label.config(fg="red")
        else:
            self.score_label.config(fg="blue")

    def clear_staff(self):
        """清除五线谱 - 保持原有逻辑"""
        self.staff_renderer.clear_notes()
        self.violin_fingerboard.clear_highlight()
        self.current_note = None
        self.show_answer = False
        self.answer_button.config(text="显示答案")

        # 如果是视奏模式，重置分数
        if self.current_mode == "sight_reading":
            self.score = 0
            self.update_score_display()

        self.update_note_info_display()

def main():
    """主函数"""
    root = tk.Tk()
    app = StaffPracticeApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
