# 🎻 小提琴练习程序

钢琴和小提琴视奏练习的Python程序，帮助学习者快速识别五线谱音符并掌握指板位置。

## 📁 项目结构

```
violin/
├── README.md                           # 项目说明文档
├── run_tests.py                        # 测试运行器
├── 
├── 📱 主程序文件
├── staff_practice_app_final.py         # ⭐ 推荐主程序（完整版）
├── violin_practice_app.py              # 纯小提琴版本
├── staff_practice_app_integrated.py    # 集成版本
├── staff_practice_app_with_violin.py   # 带小提琴版本
├── 
├── 🔧 核心模块
├── violin_fingerboard.py               # 小提琴指板数据和视图
├── staff_practice_violin_fingerboard.py # 小提琴指板集成模块
├── 
├── 🛠️ 工具脚本
├── demo_violin_features.py             # 功能演示脚本
├── 
├── 📄 文档
├── FINAL_VERIFICATION_REPORT.md        # 最终验证报告
├── VERIFICATION_REPORT.md              # 验证报告
├── 
└── 🧪 测试目录
    ├── test/
    │   ├── __init__.py                  # 测试包初始化
    │   ├── test_final_integration.py    # 最终集成测试
    │   ├── test_final_positions.py      # 最终位置测试
    │   ├── test_note_positions.py       # 音符位置测试
    │   ├── test_note_precision.py       # 音符精度测试
    │   ├── test_piano_keyboard_range.py # 钢琴键盘范围测试
    │   ├── test_piano_violin_linkage.py # 钢琴小提琴联动测试
    │   ├── test_solfege_mapping.py      # 唱名映射测试
    │   ├── test_violin_app.py           # 小提琴应用测试
    │   └── test_violin_fingerboard_changes.py # 指板变更测试
```

## 🚀 快速开始

### 启动主程序（推荐）
```bash
python staff_practice_app_final.py
```

### 其他启动选项
```bash
# 纯小提琴版本
python violin_practice_app.py

# 功能演示
python demo_violin_features.py
```

## 🧪 运行测试

### 运行所有测试
```bash
python run_tests.py
```

### 运行特定测试
```bash
python run_tests.py -t test_violin_app
```

### 列出所有测试
```bash
python run_tests.py -l
```

### 直接运行测试包
```bash
python -m test
```

## ✨ 主要功能

- 🎼 **五线谱视奏练习** - 看谱找位置，提高视奏能力
- 🎹 **钢琴键盘模式** - 点击键盘显示对应音符
- 🎻 **小提琴指板练习** - 第一把位指法练习
- 📊 **实时计分系统** - 正确+1分，错误-1分
- ⌨️ **快捷键支持** - 空格键：下一题，C键：清除
- 🔄 **双重输入支持** - 钢琴键盘和小提琴指板都可以作为输入

## 🎯 学习目标

- 快速识别五线谱上的音符
- 熟练掌握小提琴第一把位指法
- 建立音符与指板位置的对应关系
- 提高视奏反应速度

## 🔧 开发说明

### 添加新测试

1. 在 `test/` 目录下创建新的测试文件，命名格式：`test_*.py`
2. 测试文件需要包含 `main()` 函数作为入口点
3. 在测试文件开头添加路径设置：
   ```python
   import sys
   import os
   # 添加父目录到Python路径
   sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
   ```
4. 更新 `test/__init__.py` 中的 `TEST_MODULES` 列表

### 项目依赖

- Python 3.6+
- tkinter (通常随Python安装)
- 无其他外部依赖

## 📝 版本历史

- v1.0.0 - 初始版本，包含基本的视奏练习功能
- v1.1.0 - 添加小提琴指板支持
- v1.2.0 - 重构测试结构，改进代码组织

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
