#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器 - 运行所有测试
"""

import sys
import os
import argparse
from pathlib import Path

def setup_python_path():
    """设置Python路径"""
    current_dir = Path(__file__).parent.absolute()
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))

def run_all_tests():
    """运行所有测试"""
    setup_python_path()
    
    try:
        from test import run_all_tests
        return run_all_tests()
    except ImportError as e:
        print(f"❌ 导入测试模块失败: {e}")
        return False

def run_specific_test(test_name):
    """运行特定测试"""
    setup_python_path()
    
    try:
        import importlib
        module = importlib.import_module(f"test.{test_name}")
        
        if hasattr(module, 'main'):
            print(f"🧪 运行测试: {test_name}")
            print("=" * 50)
            result = module.main()
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
            return result
        else:
            print(f"❌ {test_name} 没有main函数")
            return False
            
    except ImportError as e:
        print(f"❌ 导入测试模块 {test_name} 失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 运行测试 {test_name} 出错: {e}")
        return False

def list_tests():
    """列出所有可用的测试"""
    setup_python_path()
    
    try:
        from test import TEST_MODULES
        print("📋 可用的测试模块:")
        print("=" * 30)
        for i, test_name in enumerate(TEST_MODULES, 1):
            print(f"{i:2d}. {test_name}")
        print()
        print("💡 使用方法:")
        print("  python run_tests.py                    # 运行所有测试")
        print("  python run_tests.py -t test_name       # 运行特定测试")
        print("  python run_tests.py -l                 # 列出所有测试")
        
    except ImportError as e:
        print(f"❌ 导入测试模块失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="小提琴练习程序测试运行器")
    parser.add_argument("-t", "--test", help="运行特定测试")
    parser.add_argument("-l", "--list", action="store_true", help="列出所有测试")
    
    args = parser.parse_args()
    
    if args.list:
        list_tests()
    elif args.test:
        success = run_specific_test(args.test)
        sys.exit(0 if success else 1)
    else:
        success = run_all_tests()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
