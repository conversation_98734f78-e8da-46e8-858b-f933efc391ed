#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小提琴练习程序功能演示脚本
展示所有实现的功能
"""

from violin_fingerboard import ViolinFingerboardData

def demo_fingerboard_layout():
    """演示指板布局"""
    print("🎻 小提琴第一把位指板布局")
    print("=" * 50)
    
    fingerboard = ViolinFingerboardData()
    strings = ['G', 'D', 'A', 'E']
    
    # 创建表格显示
    print("弦\\指位", end="")
    for fret in range(5):
        finger_name = "空弦" if fret == 0 else f"{fret}指"
        print(f"\t{finger_name}", end="")
    print()
    
    print("-" * 60)
    
    for string in strings:
        print(f"{string}弦", end="")
        for fret in range(5):
            position = fingerboard.get_position(string, fret)
            if position:
                sharp_mark = "#" if position.is_sharp else ""
                note_display = f"{position.note_name}{sharp_mark}{position.octave}"
                print(f"\t{note_display}", end="")
        print()
    
    print()

def demo_note_ranges():
    """演示音符范围"""
    print("🎵 音符范围统计")
    print("=" * 30)
    
    fingerboard = ViolinFingerboardData()
    all_positions = fingerboard.get_all_positions()
    
    # 统计音符
    note_counts = {}
    octave_counts = {}
    
    for pos in all_positions:
        # 统计音名
        note_name = pos.note_name
        if pos.is_sharp:
            note_name += "#"
        note_counts[note_name] = note_counts.get(note_name, 0) + 1
        
        # 统计八度
        octave_counts[pos.octave] = octave_counts.get(pos.octave, 0) + 1
    
    print("音名分布:")
    for note, count in sorted(note_counts.items()):
        print(f"  {note}: {count}次")
    
    print("\n八度分布:")
    for octave, count in sorted(octave_counts.items()):
        print(f"  {octave}八度: {count}个音符")
    
    print(f"\n总计: {len(all_positions)}个位置")
    print()

def demo_practice_scenarios():
    """演示练习场景"""
    print("🎯 练习场景演示")
    print("=" * 30)
    
    fingerboard = ViolinFingerboardData()
    
    # 场景1：基础练习（只用1-2指）
    print("场景1: 基础练习（1-2指）")
    basic_positions = []
    for string in ['G', 'D', 'A', 'E']:
        for fret in [1, 2]:
            pos = fingerboard.get_position(string, fret)
            if pos:
                basic_positions.append(pos)
    
    print(f"  包含 {len(basic_positions)} 个位置:")
    for pos in basic_positions:
        sharp_mark = "#" if pos.is_sharp else ""
        print(f"    {pos.string}弦{pos.fret}指: {pos.note_name}{sharp_mark}{pos.octave}")
    
    # 场景2：进阶练习（3-4指）
    print("\n场景2: 进阶练习（3-4指）")
    advanced_positions = []
    for string in ['G', 'D', 'A', 'E']:
        for fret in [3, 4]:
            pos = fingerboard.get_position(string, fret)
            if pos:
                advanced_positions.append(pos)
    
    print(f"  包含 {len(advanced_positions)} 个位置:")
    for pos in advanced_positions:
        sharp_mark = "#" if pos.is_sharp else ""
        print(f"    {pos.string}弦{pos.fret}指: {pos.note_name}{sharp_mark}{pos.octave}")
    
    # 场景3：特定音名练习
    print("\n场景3: 特定音名练习（所有A音）")
    a_positions = [pos for pos in fingerboard.get_all_positions() 
                   if pos.note_name == 'A']
    
    print(f"  包含 {len(a_positions)} 个A音位置:")
    for pos in a_positions:
        sharp_mark = "#" if pos.is_sharp else ""
        print(f"    {pos.string}弦{pos.fret}指: {pos.note_name}{sharp_mark}{pos.octave}")
    
    print()

def demo_ui_features():
    """演示UI功能"""
    print("🖥️  用户界面功能")
    print("=" * 30)
    
    features = [
        "✅ 五线谱显示 - 标准高音谱号，清晰的音符显示",
        "✅ 小提琴指板 - 4弦×5品位，共20个位置",
        "✅ 交互式点击 - 点击指板位置进行答题",
        "✅ 实时计分 - 正确+1分，错误-1分",
        "✅ 颜色反馈 - 绿色(正分)，红色(负分)，蓝色(零分)",
        "✅ 位置高亮 - 正确答案后高亮显示",
        "✅ 自动出题 - 正确后自动生成新题目",
        "✅ 键盘快捷键 - 空格键(下一题)，C键(清除)",
        "✅ 音符信息 - 显示当前音符和唱名",
        "✅ 响应式布局 - 左右分栏，清晰布局"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print()

def demo_learning_progression():
    """演示学习进度"""
    print("📚 学习进度建议")
    print("=" * 30)
    
    stages = [
        {
            "阶段": "1. 空弦认识",
            "内容": "熟悉G3, D4, A4, E5四个空弦音",
            "位置": "各弦0指位置"
        },
        {
            "阶段": "2. 一指练习", 
            "内容": "学习各弦一指位置",
            "位置": "G#3, D#4, A#4, F5"
        },
        {
            "阶段": "3. 二指练习",
            "内容": "学习各弦二指位置", 
            "位置": "A3, E4, B4, F#5"
        },
        {
            "阶段": "4. 三指练习",
            "内容": "学习各弦三指位置",
            "位置": "A#3, F4, C5, G5"
        },
        {
            "阶段": "5. 四指练习",
            "内容": "学习各弦四指位置",
            "位置": "B3, F#4, C#5, G#5"
        },
        {
            "阶段": "6. 综合练习",
            "内容": "随机练习所有位置",
            "位置": "全部20个位置"
        }
    ]
    
    for stage in stages:
        print(f"  {stage['阶段']}")
        print(f"    {stage['内容']}")
        print(f"    位置: {stage['位置']}")
        print()

def main():
    """主演示函数"""
    print("🎻 小提琴练习程序功能演示 🎻")
    print("=" * 60)
    print()
    
    # 运行所有演示
    demo_fingerboard_layout()
    demo_note_ranges()
    demo_practice_scenarios()
    demo_ui_features()
    demo_learning_progression()
    
    print("🎉 功能演示完成！")
    print()
    print("💡 使用提示:")
    print("1. 运行 'python violin_practice_app.py' 启动图形界面")
    print("2. 程序会随机显示一个音符在五线谱上")
    print("3. 在右侧小提琴指板上点击对应的位置")
    print("4. 正确答案会得1分并自动出下一题")
    print("5. 错误答案会扣1分但题目不变")
    print("6. 可以使用空格键手动切换题目")
    print("7. 使用C键可以清除所有内容重新开始")
    print()
    print("🎯 学习目标:")
    print("- 快速识别五线谱上的音符")
    print("- 熟练掌握小提琴第一把位指法")
    print("- 建立音符与指板位置的对应关系")
    print("- 提高视奏反应速度")

if __name__ == "__main__":
    main()
