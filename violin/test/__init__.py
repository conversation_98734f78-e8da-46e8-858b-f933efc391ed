#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小提琴练习程序测试包
包含所有测试模块
"""

__version__ = "1.0.0"
__author__ = "Violin Practice App"

# 测试模块列表
TEST_MODULES = [
    "test_final_integration",
    "test_final_positions",
    "test_note_positions",
    "test_note_precision",
    "test_piano_keyboard_range",
    "test_piano_violin_linkage",
    "test_solfege_mapping",
    "test_violin_app",
    "test_violin_fingerboard_changes",
    "test_improvements",
    "test_ui_improvements"
]

def run_all_tests():
    """运行所有测试"""
    import importlib
    import sys
    
    print("🧪 运行所有测试...")
    print("=" * 50)
    
    passed = 0
    total = 0
    
    for module_name in TEST_MODULES:
        try:
            print(f"\n📋 运行 {module_name}...")
            module = importlib.import_module(f".{module_name}", package=__name__)
            
            if hasattr(module, 'main'):
                # 运行测试模块的main函数
                result = module.main()
                total += 1
                if result:
                    passed += 1
                    print(f"✅ {module_name} 通过")
                else:
                    print(f"❌ {module_name} 失败")
            else:
                print(f"⚠️  {module_name} 没有main函数")
                
        except Exception as e:
            print(f"❌ {module_name} 出错: {e}")
            total += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    run_all_tests()
