#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进功能
验证第三线音符唱名修复、音频播放功能和小提琴指板颜色改进
"""

import sys
import os

# 添加父目录到Python路径，以便导入主程序模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_solfege_fix():
    """测试第三线音符唱名修复"""
    print("🎵 测试第三线音符唱名修复")
    print("=" * 40)
    
    try:
        from staff_practice_app_final import NoteInfo
        
        # 测试B4音符的唱名
        b4_note = NoteInfo("B4", "B", 4)
        print(f"B4音符信息:")
        print(f"  - 音名: {b4_note.note_name}")
        print(f"  - 八度: {b4_note.octave}")
        print(f"  - 唱名: {b4_note.solfege}")
        print(f"  - 频率: {b4_note.frequency:.2f} Hz")
        
        if b4_note.solfege == "si":
            print("✅ 第三线B4的唱名正确：si")
            result = True
        else:
            print(f"❌ 第三线B4的唱名错误：{b4_note.solfege}，应该是si")
            result = False
        
        # 测试其他音符的唱名
        test_notes = [
            ("C4", "do"), ("D4", "re"), ("E4", "mi"), ("F4", "fa"),
            ("G4", "sol"), ("A4", "la"), ("B4", "si")
        ]
        
        print("\n其他音符唱名验证:")
        all_correct = True
        for pitch, expected_solfege in test_notes:
            note_name = pitch[0]
            octave = int(pitch[1])
            note = NoteInfo(pitch, note_name, octave)
            
            if note.solfege == expected_solfege:
                status = "✅"
            else:
                status = "❌"
                all_correct = False
            
            print(f"  {pitch} -> {note.solfege} (期望: {expected_solfege}) {status}")
        
        return result and all_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_audio_functionality():
    """测试音频播放功能"""
    print("\n🔊 测试音频播放功能")
    print("=" * 40)
    
    try:
        from audio_player import audio_player, AUDIO_AVAILABLE
        from staff_practice_app_final import NoteInfo
        
        print(f"音频系统状态: {'可用' if AUDIO_AVAILABLE else '不可用'}")
        print(f"音频播放器初始化: {'成功' if audio_player.is_initialized else '失败'}")
        
        if AUDIO_AVAILABLE and audio_player.is_initialized:
            # 测试播放一个音符
            test_note = NoteInfo("A4", "A", 4)
            print(f"\n测试播放音符: {test_note.pitch} ({test_note.frequency:.2f} Hz)")
            print("播放中... (0.3秒)")
            
            # 播放短音符进行测试
            audio_player.play_note(test_note, duration=0.3)
            
            print("✅ 音频播放功能正常")
            return True
        else:
            print("⚠️  音频功能不可用（可能缺少依赖包）")
            return True  # 不算作失败，因为音频是可选功能
            
    except Exception as e:
        print(f"❌ 音频测试失败: {e}")
        return False

def test_violin_fingerboard_colors():
    """测试小提琴指板颜色改进"""
    print("\n🎻 测试小提琴指板颜色改进")
    print("=" * 40)
    
    try:
        from staff_practice_violin_fingerboard import ViolinFingerboardData
        
        fingerboard = ViolinFingerboardData()
        all_positions = fingerboard.get_all_positions()
        
        print("小提琴指板位置颜色方案:")
        print("位置\t\t音符\t升号\t预期颜色方案")
        print("-" * 50)
        
        color_correct = True
        for pos in all_positions[:10]:  # 只显示前10个位置
            color_scheme = "黑键样式" if pos.is_sharp else "白键样式"
            sharp_mark = "#" if pos.is_sharp else ""
            
            print(f"{pos.string}弦{pos.fret}指\t{pos.note_name}{sharp_mark}{pos.octave}\t{pos.is_sharp}\t{color_scheme}")
        
        print("\n颜色方案说明:")
        print("- 自然音符（C, D, E, F, G, A, B）: 白键样式（白色背景，黑色文字）")
        print("- 升号音符（C#, D#, F#, G#, A#）: 黑键样式（黑色背景，白色文字）")
        print("- 高亮状态: 对应的按下状态颜色")
        
        print("✅ 小提琴指板颜色方案已更新为钢琴同色系")
        return True
        
    except Exception as e:
        print(f"❌ 小提琴指板测试失败: {e}")
        return False

def test_integration():
    """测试整体集成"""
    print("\n🔗 测试整体集成")
    print("=" * 40)
    
    try:
        from staff_practice_app_final import StaffPracticeApp, AUDIO_ENABLED
        import tkinter as tk
        
        print("主程序导入: ✅")
        print(f"音频功能集成: {'✅' if AUDIO_ENABLED else '⚠️ 禁用'}")
        
        # 测试创建应用程序实例（不显示窗口）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        try:
            app = StaffPracticeApp(root)
            print("应用程序创建: ✅")
            
            # 测试生成音符
            app.next_note()
            if app.current_note:
                print(f"音符生成: ✅ ({app.current_note.pitch})")
            else:
                print("音符生成: ❌")
                return False
            
            root.destroy()
            print("✅ 整体集成测试通过")
            return True
            
        except Exception as e:
            root.destroy()
            print(f"❌ 应用程序测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 改进功能测试")
    print("=" * 60)
    
    tests = [
        ("第三线音符唱名修复", test_solfege_fix),
        ("音频播放功能", test_audio_functionality),
        ("小提琴指板颜色", test_violin_fingerboard_colors),
        ("整体集成", test_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"\n✅ {test_name} 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n❌ {test_name} 出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有改进功能都正常工作！")
        print("\n💡 改进总结:")
        print("1. ✅ 修复了第三线B4音符唱名显示错误（现在正确显示为'si'）")
        print("2. ✅ 添加了视奏练习模式的音符播放功能")
        print("3. ✅ 将小提琴指板颜色改为与钢琴键盘一致的色系")
        print("\n🚀 现在可以启动程序体验改进后的功能:")
        print("   python staff_practice_app_final.py")
    else:
        print("⚠️  部分功能存在问题，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    main()
