#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小提琴指板点击性能
验证点击响应速度和准确性的改进
"""

import sys
import os
import time
import threading

# 添加父目录到Python路径，以便导入主程序模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_click_detection_range():
    """测试点击检测范围"""
    print("🎯 测试点击检测范围")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from staff_practice_violin_fingerboard import ViolinFingerboard
        
        # 创建测试画布
        root = tk.Tk()
        root.withdraw()
        
        try:
            canvas = tk.Canvas(root, width=400, height=550, bg="white")
            fingerboard = ViolinFingerboard(canvas, 400, 550)
            
            # 测试不同位置的点击检测
            test_coordinates = [
                # (x, y, 描述)
                (120, 100, "G弦1指位置"),
                (180, 150, "D弦2指位置"),
                (240, 200, "A弦3指位置"),
                (300, 250, "E弦4指位置"),
                (110, 95, "G弦1指边缘"),   # 稍微偏移
                (190, 155, "D弦2指边缘"),  # 稍微偏移
                (50, 50, "指板外区域"),     # 应该检测不到
                (350, 500, "指板外区域"),   # 应该检测不到
            ]
            
            print("坐标检测测试:")
            print("X坐标\tY坐标\t描述\t\t检测结果")
            print("-" * 60)
            
            detection_count = 0
            for x, y, description in test_coordinates:
                position = fingerboard._get_position_at_coordinates(x, y)
                if position:
                    result = f"{position.string}弦{position.fret}指 ({position.note_name})"
                    detection_count += 1
                else:
                    result = "未检测到"
                
                print(f"{x}\t{y}\t{description:<15}\t{result}")
            
            print(f"\n检测成功率: {detection_count}/{len(test_coordinates)}")
            
            # 测试边界情况
            print("\n边界检测测试:")
            boundary_tests = [
                (fingerboard.margin_left - 10, fingerboard.margin_top + 50, "左边界外"),
                (fingerboard.margin_left + 10, fingerboard.margin_top + 50, "左边界内"),
                (fingerboard.margin_left + fingerboard.board_width - 10, fingerboard.margin_top + 50, "右边界内"),
                (fingerboard.margin_left + fingerboard.board_width + 10, fingerboard.margin_top + 50, "右边界外"),
            ]
            
            for x, y, description in boundary_tests:
                position = fingerboard._get_position_at_coordinates(x, y)
                result = "检测到" if position else "未检测到"
                print(f"{description}: {result}")
            
            root.destroy()
            return True
            
        except Exception as e:
            root.destroy()
            print(f"❌ 指板测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 点击检测测试失败: {e}")
        return False

def test_highlight_performance():
    """测试高亮性能"""
    print("\n⚡ 测试高亮性能")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from staff_practice_violin_fingerboard import ViolinFingerboard
        
        # 创建测试画布
        root = tk.Tk()
        root.withdraw()
        
        try:
            canvas = tk.Canvas(root, width=400, height=550, bg="white")
            fingerboard = ViolinFingerboard(canvas, 400, 550)
            
            # 测试高亮性能
            test_positions = [
                ("G", 1), ("D", 2), ("A", 3), ("E", 4),
                ("G", 0), ("D", 0), ("A", 0), ("E", 0)
            ]
            
            print("高亮性能测试:")
            total_time = 0
            
            for string, fret in test_positions:
                start_time = time.time()
                fingerboard.highlight_position(string, fret)
                end_time = time.time()
                
                duration = (end_time - start_time) * 1000  # 转换为毫秒
                total_time += duration
                
                print(f"{string}弦{fret}指高亮: {duration:.2f}ms")
                
                # 短暂延迟以模拟实际使用
                time.sleep(0.01)
            
            # 测试清除高亮
            start_time = time.time()
            fingerboard.clear_highlight()
            end_time = time.time()
            
            clear_duration = (end_time - start_time) * 1000
            total_time += clear_duration
            
            print(f"清除高亮: {clear_duration:.2f}ms")
            print(f"平均高亮时间: {total_time / (len(test_positions) + 1):.2f}ms")
            
            # 性能评估
            avg_time = total_time / (len(test_positions) + 1)
            if avg_time < 10:
                print("✅ 高亮性能优秀 (<10ms)")
            elif avg_time < 50:
                print("✅ 高亮性能良好 (<50ms)")
            else:
                print("⚠️  高亮性能需要优化 (>50ms)")
            
            root.destroy()
            return avg_time < 100  # 100ms以内认为可接受
            
        except Exception as e:
            root.destroy()
            print(f"❌ 高亮性能测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 高亮性能测试失败: {e}")
        return False

def test_click_callback_performance():
    """测试点击回调性能"""
    print("\n🔄 测试点击回调性能")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from staff_practice_violin_fingerboard import ViolinFingerboard, ViolinPosition
        
        # 创建测试画布
        root = tk.Tk()
        root.withdraw()
        
        try:
            canvas = tk.Canvas(root, width=400, height=550, bg="white")
            fingerboard = ViolinFingerboard(canvas, 400, 550)
            
            # 记录回调执行时间
            callback_times = []
            
            def test_callback(position: ViolinPosition):
                start_time = time.time()
                # 模拟实际的回调处理
                time.sleep(0.001)  # 模拟1ms的处理时间
                end_time = time.time()
                callback_times.append((end_time - start_time) * 1000)
            
            fingerboard.set_position_click_callback(test_callback)
            
            # 模拟多次点击
            test_clicks = [
                (120, 100), (180, 150), (240, 200), (300, 250),
                (120, 200), (180, 250), (240, 300), (300, 350)
            ]
            
            print("点击回调测试:")
            for i, (x, y) in enumerate(test_clicks):
                # 创建模拟点击事件
                class MockEvent:
                    def __init__(self, x, y):
                        self.x = x
                        self.y = y
                
                event = MockEvent(x, y)
                start_time = time.time()
                fingerboard._on_click(event)
                
                # 等待回调完成
                root.update()
                end_time = time.time()
                
                total_time = (end_time - start_time) * 1000
                print(f"点击 {i+1}: 总时间 {total_time:.2f}ms")
            
            if callback_times:
                avg_callback_time = sum(callback_times) / len(callback_times)
                print(f"平均回调时间: {avg_callback_time:.2f}ms")
                
                if avg_callback_time < 5:
                    print("✅ 回调性能优秀 (<5ms)")
                elif avg_callback_time < 20:
                    print("✅ 回调性能良好 (<20ms)")
                else:
                    print("⚠️  回调性能需要优化 (>20ms)")
            
            root.destroy()
            return len(callback_times) > 0
            
        except Exception as e:
            root.destroy()
            print(f"❌ 回调性能测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 点击回调测试失败: {e}")
        return False

def test_responsiveness_improvements():
    """测试响应性改进"""
    print("\n🚀 测试响应性改进")
    print("=" * 40)
    
    improvements = [
        "✅ 扩大点击检测范围（15px → 25px误差）",
        "✅ 优化边界检测逻辑",
        "✅ 单个位置重绘替代全局重绘",
        "✅ 异步回调处理（after_idle）",
        "✅ 立即视觉反馈（光标变化）",
        "✅ 扩大画布尺寸（350x500 → 400x550）",
        "✅ 增加边距以显示完整文字"
    ]
    
    print("已实施的性能改进:")
    for improvement in improvements:
        print(f"  {improvement}")
    
    print("\n💡 使用建议:")
    print("1. 点击音符圆圈中心获得最佳响应")
    print("2. 允许一定的点击偏差，系统会自动校正")
    print("3. 如果仍有响应问题，请检查系统性能")
    
    return True

def main():
    """主测试函数"""
    print("🧪 小提琴指板点击性能测试")
    print("=" * 60)
    
    tests = [
        ("点击检测范围", test_click_detection_range),
        ("高亮性能", test_highlight_performance),
        ("点击回调性能", test_click_callback_performance),
        ("响应性改进总结", test_responsiveness_improvements),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"\n✅ {test_name} 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n❌ {test_name} 出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 点击性能优化成功！")
        print("\n🔧 主要改进:")
        print("- 扩大了点击检测范围")
        print("- 优化了高亮重绘性能")
        print("- 添加了即时视觉反馈")
        print("- 使用异步回调处理")
        print("- 扩大了显示区域")
        print("\n🚀 现在可以启动程序测试改进效果:")
        print("   python staff_practice_app_final.py")
    else:
        print("⚠️  部分性能测试存在问题，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    main()
