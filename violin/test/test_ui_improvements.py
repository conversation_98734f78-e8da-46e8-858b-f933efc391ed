#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI改进
验证小提琴指板界面的改进：去掉黄色背景、调整标题位置、移除弦名标签
"""

import sys
import os

# 添加父目录到Python路径，以便导入主程序模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_canvas_background():
    """测试画布背景改进"""
    print("🎨 测试画布背景改进")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from staff_practice_app_final import StaffPracticeApp
        
        # 创建测试窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        try:
            app = StaffPracticeApp(root)
            
            # 检查小提琴画布的背景色
            canvas_bg = app.violin_canvas.cget("bg")
            canvas_relief = app.violin_canvas.cget("relief")
            canvas_borderwidth = app.violin_canvas.cget("borderwidth")
            
            print(f"小提琴画布背景色: {canvas_bg}")
            print(f"画布边框样式: {canvas_relief}")
            print(f"画布边框宽度: {canvas_borderwidth}")
            
            # 验证改进
            improvements = []
            if canvas_bg == "white":
                improvements.append("✅ 背景色已改为白色（去掉黄色）")
            else:
                improvements.append(f"❌ 背景色仍为: {canvas_bg}")
            
            if canvas_relief == "flat":
                improvements.append("✅ 边框样式已改为平面")
            else:
                improvements.append(f"❌ 边框样式仍为: {canvas_relief}")
            
            if str(canvas_borderwidth) == "0":
                improvements.append("✅ 边框宽度已设为0")
            else:
                improvements.append(f"❌ 边框宽度仍为: {canvas_borderwidth}")
            
            for improvement in improvements:
                print(improvement)
            
            root.destroy()
            return all("✅" in imp for imp in improvements)
            
        except Exception as e:
            root.destroy()
            print(f"❌ 应用程序测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 画布背景测试失败: {e}")
        return False

def test_title_position():
    """测试标题位置调整"""
    print("\n📝 测试标题位置调整")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from staff_practice_violin_fingerboard import ViolinFingerboard
        
        # 创建测试画布
        root = tk.Tk()
        root.withdraw()
        
        try:
            canvas = tk.Canvas(root, width=350, height=500, bg="white")
            fingerboard = ViolinFingerboard(canvas, 350, 500)
            
            # 检查画布上的文本对象
            canvas_items = canvas.find_all()
            title_found = False
            title_y_position = None
            
            for item in canvas_items:
                if canvas.type(item) == "text":
                    text_content = canvas.itemcget(item, "text")
                    if "小提琴第一把位" in text_content:
                        title_found = True
                        coords = canvas.coords(item)
                        title_y_position = coords[1] if coords else None
                        break
            
            if title_found:
                print(f"✅ 找到标题: '小提琴第一把位'")
                print(f"标题Y坐标: {title_y_position}")
                
                if title_y_position and title_y_position <= 10:
                    print("✅ 标题位置已上移（Y坐标 ≤ 10）")
                    result = True
                else:
                    print(f"❌ 标题位置可能需要进一步上移（Y坐标: {title_y_position}）")
                    result = False
            else:
                print("❌ 未找到标题")
                result = False
            
            root.destroy()
            return result
            
        except Exception as e:
            root.destroy()
            print(f"❌ 指板测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 标题位置测试失败: {e}")
        return False

def test_string_labels_removal():
    """测试弦名标签移除"""
    print("\n🎻 测试弦名标签移除")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from staff_practice_violin_fingerboard import ViolinFingerboard
        
        # 创建测试画布
        root = tk.Tk()
        root.withdraw()
        
        try:
            canvas = tk.Canvas(root, width=350, height=500, bg="white")
            fingerboard = ViolinFingerboard(canvas, 350, 500)
            
            # 检查画布上的文本对象，寻找弦名标签
            canvas_items = canvas.find_all()
            string_labels_found = []

            for item in canvas_items:
                if canvas.type(item) == "text":
                    text_content = canvas.itemcget(item, "text")
                    coords = canvas.coords(item)

                    # 弦名标签应该在指板顶部（Y坐标较小）且只有单个字母
                    if (text_content in ["G", "D", "A", "E"] and
                        len(text_content) == 1 and
                        coords and coords[1] < 30):  # Y坐标小于30表示在顶部
                        string_labels_found.append(text_content)

            if not string_labels_found:
                print("✅ 弦名标签已成功移除")
                print("指板顶部不再显示 G、D、A、E 弦名标签")
                result = True
            else:
                print(f"❌ 仍然找到弦名标签: {string_labels_found}")
                result = False
            
            # 检查其他文本内容
            other_texts = []
            for item in canvas_items:
                if canvas.type(item) == "text":
                    text_content = canvas.itemcget(item, "text")
                    if text_content not in ["G", "D", "A", "E"] and "小提琴第一把位" not in text_content:
                        other_texts.append(text_content)
            
            if other_texts:
                print(f"画布上的其他文本: {other_texts[:5]}...")  # 只显示前5个
            
            root.destroy()
            return result
            
        except Exception as e:
            root.destroy()
            print(f"❌ 指板测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 弦名标签测试失败: {e}")
        return False

def test_all_apps_consistency():
    """测试所有应用程序的一致性"""
    print("\n🔄 测试所有应用程序的一致性")
    print("=" * 40)
    
    app_files = [
        "staff_practice_app_final.py",
        "staff_practice_app_integrated.py", 
        "staff_practice_app_with_violin.py",
        "violin_practice_app.py"
    ]
    
    consistent = True
    
    for app_file in app_files:
        try:
            with open(app_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否还有黄色背景
            if 'bg="lightyellow"' in content:
                print(f"❌ {app_file} 仍有黄色背景")
                consistent = False
            else:
                print(f"✅ {app_file} 已移除黄色背景")
                
        except FileNotFoundError:
            print(f"⚠️  {app_file} 文件不存在")
        except Exception as e:
            print(f"❌ 检查 {app_file} 失败: {e}")
            consistent = False
    
    return consistent

def main():
    """主测试函数"""
    print("🧪 UI改进测试")
    print("=" * 60)
    
    tests = [
        ("画布背景改进", test_canvas_background),
        ("标题位置调整", test_title_position),
        ("弦名标签移除", test_string_labels_removal),
        ("应用程序一致性", test_all_apps_consistency),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"\n✅ {test_name} 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n❌ {test_name} 出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有UI改进都正常工作！")
        print("\n💡 UI改进总结:")
        print("1. ✅ 去掉了小提琴指板的黄色背景板")
        print("2. ✅ 将'小提琴第一把位'标题往上移动")
        print("3. ✅ 移除了蓝色的弦名标签（G D A E）")
        print("4. ✅ 所有应用程序文件保持一致")
        print("\n🚀 现在可以启动程序查看改进效果:")
        print("   python staff_practice_app_final.py")
    else:
        print("⚠️  部分UI改进存在问题，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    main()
