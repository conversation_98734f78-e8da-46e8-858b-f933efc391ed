#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音符位置的精确度
验证音符是否精确显示在线上或间中
"""

def test_note_position_precision():
    """测试音符位置精确度"""
    print("🎼 测试音符位置精确度")
    print("=" * 50)
    
    # 五线谱参数
    staff_top = 50
    line_spacing = 15
    middle_line_y = staff_top + 2 * line_spacing  # 第三线 = 80
    half_spacing = line_spacing / 2.0  # 7.5
    
    print(f"五线谱参数:")
    print(f"  staff_top = {staff_top}")
    print(f"  line_spacing = {line_spacing}")
    print(f"  middle_line_y = {middle_line_y} (第三线)")
    print(f"  half_spacing = {half_spacing}")
    print()
    
    # 五线谱线的Y坐标
    staff_lines = {
        "第一线": staff_top + 4 * line_spacing,  # 110
        "第二线": staff_top + 3 * line_spacing,  # 95
        "第三线": staff_top + 2 * line_spacing,  # 80 (middle_line_y)
        "第四线": staff_top + 1 * line_spacing,  # 65
        "第五线": staff_top + 0 * line_spacing,  # 50
        "下加一线": staff_top + 5 * line_spacing,  # 125
        "上加一线": staff_top - 1 * line_spacing,  # 35
        "上加二线": staff_top - 2 * line_spacing,  # 20
    }
    
    print("五线谱线的Y坐标:")
    for line_name, y_coord in staff_lines.items():
        print(f"  {line_name}: Y = {y_coord}")
    print()
    
    # 测试关键音符的位置
    test_notes = [
        # 在线上的音符
        ('C4', -6, "下加一线", 125),
        ('E4', -4, "第一线", 110),
        ('G4', -2, "第二线", 95),
        ('B4', 0, "第三线", 80),
        ('D5', 2, "第四线", 65),
        ('F5', 4, "第五线", 50),
        ('A5', 6, "上加一线", 35),
        ('C6', 8, "上加二线", 20),
        
        # 在间中的音符
        ('D4', -5, "下加一线与第一线间", 117.5),
        ('F4', -3, "第一间", 102.5),
        ('A4', -1, "第二间", 87.5),
        ('C5', 1, "第三间", 72.5),
        ('E5', 3, "第四间", 57.5),
        ('G5', 5, "第五线上方", 42.5),
        ('B5', 7, "上加一线上方", 27.5),
    ]
    
    print("音符位置计算验证:")
    print("音符\tstaff_y\t计算Y值\t期望Y值\t位置描述\t\t状态")
    print("-" * 80)
    
    all_correct = True
    
    for note, staff_y, description, expected_y in test_notes:
        # 使用程序中的计算公式
        calculated_y = int(middle_line_y - staff_y * half_spacing)
        
        # 检查是否匹配
        if abs(calculated_y - expected_y) <= 0.5:  # 允许0.5像素的误差
            status = "✅"
        else:
            status = "❌"
            all_correct = False
        
        print(f"{note}\t{staff_y}\t{calculated_y}\t{expected_y}\t{description:<20}\t{status}")
    
    print("\n" + "=" * 50)
    
    if all_correct:
        print("✅ 所有音符位置计算精确！")
        print("\n📝 精确度说明:")
        print("- 使用浮点数除法: half_spacing = line_spacing / 2.0")
        print("- 在线上的音符: 精确显示在线的中心")
        print("- 在间中的音符: 精确显示在间的中心")
        print("- 计算公式: y = int(middle_line_y - staff_y * half_spacing)")
    else:
        print("❌ 部分音符位置计算有误")
    
    return all_correct

def test_visual_alignment():
    """测试视觉对齐"""
    print("\n👁️  测试视觉对齐")
    print("=" * 30)
    
    # 关键验证点
    alignment_tests = [
        {
            "音符": "C4",
            "位置": "下加一线",
            "要求": "音符中心与下加一线重合",
            "验证": "在钢琴键盘模式下点击C4，检查音符是否在下加一线正中间"
        },
        {
            "音符": "B4", 
            "位置": "第三线",
            "要求": "音符中心与第三线重合",
            "验证": "在钢琴键盘模式下点击B4，检查音符是否在第三线正中间"
        },
        {
            "音符": "C5",
            "位置": "第三间",
            "要求": "音符中心在第三线和第四线正中间",
            "验证": "在钢琴键盘模式下点击C5，检查音符是否在第三间正中间"
        },
        {
            "音符": "F4",
            "位置": "第一间", 
            "要求": "音符中心在第一线和第二线正中间",
            "验证": "在钢琴键盘模式下点击F4，检查音符是否在第一间正中间"
        }
    ]
    
    print("关键对齐验证点:")
    for i, test in enumerate(alignment_tests, 1):
        print(f"\n{i}. {test['音符']} ({test['位置']}):")
        print(f"   要求: {test['要求']}")
        print(f"   验证: {test['验证']}")
    
    print(f"\n💡 验证方法:")
    print(f"1. 运行主程序: python staff_practice_app_final.py")
    print(f"2. 选择'钢琴键盘'模式")
    print(f"3. 依次点击上述音符，观察位置是否精确")
    print(f"4. 特别注意间中的音符是否在两条线的正中间")

def main():
    """主函数"""
    print("🎯 音符位置精确度验证")
    print("验证音符是否精确显示在线上或间中")
    print()
    
    # 运行测试
    result = test_note_position_precision()
    test_visual_alignment()
    
    print(f"\n{'='*50}")
    if result:
        print("🎉 音符位置计算完全精确！")
        print("\n✨ 改进要点:")
        print("- 使用浮点数除法确保精度")
        print("- 线上音符精确居中")
        print("- 间中音符精确居中")
        print("- 加线音符位置准确")
        
        print("\n🚀 现在可以测试主程序:")
        print("python staff_practice_app_final.py")
        print("\n特别验证:")
        print("- C4应该精确在下加一线上")
        print("- C5应该精确在第三间中")
        print("- 所有音符显示清晰、位置准确")
    else:
        print("⚠️  音符位置仍需调整")
    
    return result

if __name__ == "__main__":
    main()
