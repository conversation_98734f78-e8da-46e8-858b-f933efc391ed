#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标题显示修复
验证"小提琴第一把位"标题能够完整显示
"""

import sys
import os

# 添加父目录到Python路径，以便导入主程序模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_title_position_and_visibility():
    """测试标题位置和可见性"""
    print("📝 测试标题位置和可见性")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from staff_practice_violin_fingerboard import ViolinFingerboard
        
        # 创建测试画布
        root = tk.Tk()
        root.withdraw()
        
        try:
            canvas = tk.Canvas(root, width=400, height=580, bg="white")
            fingerboard = ViolinFingerboard(canvas, 400, 580)
            
            # 检查画布上的标题文本
            canvas_items = canvas.find_all()
            title_found = False
            title_info = None
            
            for item in canvas_items:
                if canvas.type(item) == "text":
                    text_content = canvas.itemcget(item, "text")
                    if "小提琴第一把位" in text_content:
                        title_found = True
                        coords = canvas.coords(item)
                        font = canvas.itemcget(item, "font")
                        title_info = {
                            "text": text_content,
                            "x": coords[0] if coords else None,
                            "y": coords[1] if coords else None,
                            "font": font
                        }
                        break
            
            if title_found and title_info:
                print(f"✅ 找到标题: '{title_info['text']}'")
                print(f"标题位置: X={title_info['x']}, Y={title_info['y']}")
                print(f"标题字体: {title_info['font']}")
                
                # 检查标题是否在安全区域内
                y_pos = title_info['y']
                margin_top = fingerboard.margin_top
                
                print(f"上边距: {margin_top}")
                print(f"标题Y坐标: {y_pos}")
                
                # 验证标题位置
                if y_pos >= 15:  # 至少距离顶部15像素
                    print("✅ 标题位置安全，有足够的上边距")
                    position_ok = True
                else:
                    print(f"❌ 标题位置过高，可能被截断 (Y={y_pos})")
                    position_ok = False
                
                # 验证标题在上边距范围内
                if y_pos < margin_top:
                    print("✅ 标题在上边距范围内")
                    margin_ok = True
                else:
                    print(f"❌ 标题超出上边距范围 (Y={y_pos}, 上边距={margin_top})")
                    margin_ok = False
                
                return position_ok and margin_ok
            else:
                print("❌ 未找到标题")
                return False
            
        except Exception as e:
            print(f"❌ 指板测试失败: {e}")
            return False
        finally:
            root.destroy()
            
    except Exception as e:
        print(f"❌ 标题显示测试失败: {e}")
        return False

def test_canvas_size_adjustment():
    """测试画布尺寸调整"""
    print("\n📏 测试画布尺寸调整")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from staff_practice_app_final import StaffPracticeApp
        
        # 创建测试窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        try:
            app = StaffPracticeApp(root)
            
            # 检查画布尺寸
            canvas_width = app.violin_canvas.winfo_reqwidth()
            canvas_height = app.violin_canvas.winfo_reqheight()
            
            print(f"小提琴画布尺寸: {canvas_width} x {canvas_height}")
            
            # 验证尺寸改进
            improvements = []
            
            if canvas_width >= 400:
                improvements.append("✅ 画布宽度符合要求 (≥400)")
            else:
                improvements.append(f"❌ 画布宽度不足: {canvas_width}")
            
            if canvas_height >= 580:
                improvements.append("✅ 画布高度已增加到580+")
            else:
                improvements.append(f"❌ 画布高度不足: {canvas_height}")
            
            # 检查指板边距
            fingerboard = app.violin_fingerboard
            print(f"指板上边距: {fingerboard.margin_top}")
            
            if fingerboard.margin_top >= 50:
                improvements.append("✅ 上边距已增加到50+")
            else:
                improvements.append(f"❌ 上边距不足: {fingerboard.margin_top}")
            
            for improvement in improvements:
                print(improvement)
            
            return all("✅" in imp for imp in improvements)
            
        except Exception as e:
            print(f"❌ 应用程序测试失败: {e}")
            return False
        finally:
            root.destroy()
            
    except Exception as e:
        print(f"❌ 画布尺寸测试失败: {e}")
        return False

def test_text_visibility_comprehensive():
    """测试文字可见性综合检查"""
    print("\n👁️ 测试文字可见性综合检查")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from staff_practice_violin_fingerboard import ViolinFingerboard
        
        # 创建测试画布
        root = tk.Tk()
        root.withdraw()
        
        try:
            canvas = tk.Canvas(root, width=400, height=580, bg="white")
            fingerboard = ViolinFingerboard(canvas, 400, 580)
            
            # 检查所有文本元素
            canvas_items = canvas.find_all()
            text_items = []
            
            for item in canvas_items:
                if canvas.type(item) == "text":
                    coords = canvas.coords(item)
                    text_content = canvas.itemcget(item, "text")
                    if coords:
                        text_items.append({
                            "text": text_content,
                            "x": coords[0],
                            "y": coords[1]
                        })
            
            print("文本可见性检查:")
            print("文本内容\t\tX坐标\tY坐标\t状态")
            print("-" * 60)
            
            all_visible = True
            title_visible = False
            
            for item in text_items:
                text = item["text"]
                x = item["x"]
                y = item["y"]
                
                # 检查是否在画布范围内，留出一定边距
                if 5 <= x <= 395 and 5 <= y <= 575:
                    status = "✅ 可见"
                    if "小提琴第一把位" in text:
                        title_visible = True
                else:
                    status = "❌ 超出边界"
                    all_visible = False
                
                # 截断长文本以便显示
                display_text = text[:15] + "..." if len(text) > 15 else text
                print(f"{display_text:<20}\t{x:.1f}\t{y:.1f}\t{status}")
            
            print(f"\n总体可见性: {'✅ 全部可见' if all_visible else '❌ 部分超出'}")
            print(f"标题可见性: {'✅ 标题可见' if title_visible else '❌ 标题不可见'}")
            
            return all_visible and title_visible
            
        except Exception as e:
            print(f"❌ 指板测试失败: {e}")
            return False
        finally:
            root.destroy()
            
    except Exception as e:
        print(f"❌ 文字可见性测试失败: {e}")
        return False

def test_improvement_summary():
    """测试改进总结"""
    print("\n📊 改进总结")
    print("=" * 40)
    
    improvements = {
        "画布高度": "550 → 580 像素",
        "上边距": "35 → 50 像素", 
        "标题Y坐标": "5 → 20 像素",
        "标题可见性": "部分截断 → 完整显示"
    }
    
    print("标题显示改进:")
    for item, change in improvements.items():
        print(f"✅ {item}: {change}")
    
    print("\n💡 改进效果:")
    print("- 标题'小提琴第一把位'现在完整可见")
    print("- 增加了足够的上边距空间")
    print("- 保持了整体布局的协调性")
    print("- 所有应用程序文件保持一致")
    
    return True

def main():
    """主测试函数"""
    print("🧪 标题显示修复测试")
    print("=" * 60)
    
    tests = [
        ("标题位置和可见性", test_title_position_and_visibility),
        ("画布尺寸调整", test_canvas_size_adjustment),
        ("文字可见性综合检查", test_text_visibility_comprehensive),
        ("改进总结", test_improvement_summary),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"\n✅ {test_name} 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n❌ {test_name} 出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 标题显示修复成功！")
        print("\n🔧 主要改进:")
        print("- 📏 增加画布高度到580像素")
        print("- 📝 增加上边距到50像素")
        print("- 🎯 调整标题Y坐标到20像素")
        print("- 👁️ 确保标题完整可见")
        print("\n🚀 现在可以启动程序查看改进效果:")
        print("   python staff_practice_app_final.py")
    else:
        print("⚠️  部分标题显示修复存在问题，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    main()
