#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小提琴练习程序主应用类
集成五线谱、钢琴键盘和小提琴指板
"""

import tkinter as tk
from tkinter import ttk, Canvas, Frame, Button, Label
from typing import Optional
import random

# 导入自定义模块
from violin_fingerboard import ViolinFingerboard, ViolinPosition, violin_fingerboard_data

# 简化的音符数据类
class NoteInfo:
    """音符信息类"""
    def __init__(self, pitch: str, note_name: str, octave: int, frequency: float = 440.0):
        self.pitch = pitch
        self.note_name = note_name
        self.octave = octave
        self.frequency = frequency
        self.solfege = self._get_solfege(note_name)

    def _get_solfege(self, note_name: str) -> str:
        """获取唱名"""
        solfege_map = {
            'C': 'do', 'D': 're', 'E': 'mi', 'F': 'fa',
            'G': 'sol', 'A': 'la', 'B': 'si'
        }
        return solfege_map.get(note_name, '')

class SimpleStaffRenderer:
    """简化的五线谱渲染器"""

    def __init__(self, canvas: Canvas, width: int = 400, height: int = 150):
        self.canvas = canvas
        self.width = width
        self.height = height
        self.staff_left = 50
        self.staff_right = width - 50
        self.staff_top = 30
        self.line_spacing = 15
        self.middle_line_y = self.staff_top + 2 * self.line_spacing
        self.current_note = None
        self._draw_staff()

    def _draw_staff(self):
        """绘制五线谱"""
        self.canvas.delete("all")

        # 绘制五条线
        for i in range(5):
            y = self.staff_top + i * self.line_spacing
            self.canvas.create_line(
                self.staff_left, y, self.staff_right, y,
                fill="black", width=1
            )

        # 绘制高音谱号
        self.canvas.create_text(
            self.staff_left + 15, self.middle_line_y,
            text="𝄞", font=("Arial", 30), fill="black"
        )

    def add_note(self, note_info):
        """添加音符到五线谱"""
        self.clear_notes()
        self.current_note = note_info

        # 简化的音符位置计算
        note_positions = {
            'G3': 4, 'A3': 3.5, 'B3': 3, 'C4': 2.5, 'D4': 2,
            'E4': 1.5, 'F4': 1, 'G4': 0.5, 'A4': 0, 'B4': -0.5,
            'C5': -1, 'D5': -1.5, 'E5': -2, 'F5': -2.5, 'G5': -3
        }

        y_offset = note_positions.get(note_info.pitch, 0) * self.line_spacing
        note_y = self.middle_line_y + y_offset
        note_x = self.staff_left + 80

        # 绘制音符
        self.canvas.create_oval(
            note_x - 8, note_y - 6,
            note_x + 8, note_y + 6,
            fill="black", outline="black",
            tags="note"
        )

        # 绘制符干
        if note_y > self.middle_line_y:
            # 符干向上
            self.canvas.create_line(
                note_x + 8, note_y, note_x + 8, note_y - 30,
                fill="black", width=2,
                tags="note"
            )
        else:
            # 符干向下
            self.canvas.create_line(
                note_x - 8, note_y, note_x - 8, note_y + 30,
                fill="black", width=2,
                tags="note"
            )

    def clear_notes(self):
        """清除音符"""
        self.canvas.delete("note")

# 简化的音符数据类
class NoteInfo:
    """音符信息类"""
    def __init__(self, pitch: str, note_name: str, octave: int, frequency: float = 440.0):
        self.pitch = pitch
        self.note_name = note_name
        self.octave = octave
        self.frequency = frequency
        self.solfege = self._get_solfege(note_name)
    
    def _get_solfege(self, note_name: str) -> str:
        """获取唱名"""
        solfege_map = {
            'C': 'do', 'D': 're', 'E': 'mi', 'F': 'fa',
            'G': 'sol', 'A': 'la', 'B': 'si'
        }
        return solfege_map.get(note_name, '')

class SimpleStaffRenderer:
    """简化的五线谱渲染器"""
    
    def __init__(self, canvas: Canvas, width: int = 400, height: int = 150):
        self.canvas = canvas
        self.width = width
        self.height = height
        self.staff_left = 50
        self.staff_right = width - 50
        self.staff_top = 30
        self.line_spacing = 15
        self.middle_line_y = self.staff_top + 2 * self.line_spacing
        self.current_note = None
        self._draw_staff()
    
    def _draw_staff(self):
        """绘制五线谱"""
        self.canvas.delete("all")
        
        # 绘制五条线
        for i in range(5):
            y = self.staff_top + i * self.line_spacing
            self.canvas.create_line(
                self.staff_left, y, self.staff_right, y,
                fill="black", width=1
            )
        
        # 绘制高音谱号
        self.canvas.create_text(
            self.staff_left + 15, self.middle_line_y,
            text="𝄞", font=("Arial", 30), fill="black"
        )
    
    def add_note(self, note_info):
        """添加音符到五线谱"""
        self.clear_notes()
        self.current_note = note_info
        
        # 简化的音符位置计算
        note_positions = {
            'G3': 4, 'A3': 3.5, 'B3': 3, 'C4': 2.5, 'D4': 2,
            'E4': 1.5, 'F4': 1, 'G4': 0.5, 'A4': 0, 'B4': -0.5,
            'C5': -1, 'D5': -1.5, 'E5': -2, 'F5': -2.5, 'G5': -3
        }
        
        y_offset = note_positions.get(note_info.pitch, 0) * self.line_spacing
        note_y = self.middle_line_y + y_offset
        note_x = self.staff_left + 80
        
        # 绘制音符
        self.canvas.create_oval(
            note_x - 8, note_y - 6,
            note_x + 8, note_y + 6,
            fill="black", outline="black"
        )
        
        # 绘制符干
        if note_y > self.middle_line_y:
            # 符干向上
            self.canvas.create_line(
                note_x + 8, note_y, note_x + 8, note_y - 30,
                fill="black", width=2
            )
        else:
            # 符干向下
            self.canvas.create_line(
                note_x - 8, note_y, note_x - 8, note_y + 30,
                fill="black", width=2
            )
    
    def clear_notes(self):
        """清除音符"""
        self.canvas.delete("note")
        self._draw_staff()

class ViolinPracticeApp:
    """小提琴练习应用程序"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.setup_window()
        
        # 当前音符和分数
        self.current_note: Optional[NoteInfo] = None
        self.score = 0
        
        # 创建UI组件
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
        # 开始第一个练习
        self.next_note()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("小提琴练习程序")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
    
    def create_widgets(self):
        """创建所有UI组件"""
        # 主框架
        self.main_frame = Frame(self.root)
        
        # 标题
        self.title_label = Label(
            self.main_frame,
            text="小提琴练习程序",
            font=("Arial", 16, "bold")
        )
        
        # 上半部分框架（五线谱和指板）
        self.top_frame = Frame(self.main_frame)
        
        # 五线谱框架
        self.staff_frame = Frame(self.top_frame)
        self.staff_canvas = Canvas(
            self.staff_frame,
            width=400, height=150,
            bg="white", relief="sunken", borderwidth=2
        )
        self.staff_renderer = SimpleStaffRenderer(self.staff_canvas, 400, 150)
        
        # 小提琴指板框架
        self.violin_frame = Frame(self.top_frame)
        self.violin_canvas = Canvas(
            self.violin_frame,
            width=400, height=580,
            bg="white", relief="flat", borderwidth=0
        )
        self.violin_fingerboard = ViolinFingerboard(self.violin_canvas, 400, 580)
        self.violin_fingerboard.set_position_click_callback(self.on_violin_position_click)
        
        # 信息显示框架
        self.info_frame = Frame(self.main_frame)
        
        # 音符信息显示
        self.note_info_label = Label(
            self.info_frame,
            text="当前音符: 无",
            font=("Arial", 12)
        )
        
        # 分数显示
        self.score_label = Label(
            self.info_frame,
            text="得分: 0",
            font=("Arial", 12, "bold"),
            fg="blue"
        )
        
        # 控制按钮框架
        self.control_frame = Frame(self.main_frame)
        
        self.next_button = Button(
            self.control_frame,
            text="下一题",
            command=self.next_note,
            font=("Arial", 10)
        )
        
        self.clear_button = Button(
            self.control_frame,
            text="清除",
            command=self.clear_all,
            font=("Arial", 10)
        )
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        self.title_label.pack(pady=(0, 10))
        
        # 上半部分（五线谱和指板）
        self.top_frame.pack(pady=(0, 10))
        
        # 五线谱（左侧）
        self.staff_frame.pack(side="left", padx=(0, 20))
        Label(self.staff_frame, text="五线谱", font=("Arial", 12, "bold")).pack()
        self.staff_canvas.pack()
        
        # 小提琴指板（右侧）
        self.violin_frame.pack(side="left")
        self.violin_canvas.pack()
        
        # 信息显示
        self.info_frame.pack(pady=(0, 10))
        self.note_info_label.pack()
        self.score_label.pack(pady=(5, 0))
        
        # 控制按钮
        self.control_frame.pack()
        self.next_button.pack(side="left", padx=(0, 10))
        self.clear_button.pack(side="left")
    
    def bind_events(self):
        """绑定事件"""
        self.root.bind("<KeyPress-space>", lambda e: self.next_note())
        self.root.bind("<KeyPress-c>", lambda e: self.clear_all())
    
    def next_note(self):
        """显示下一个随机音符"""
        # 从小提琴指板数据中随机选择一个位置
        all_positions = violin_fingerboard_data.get_all_positions()
        # 只选择非空弦位置
        non_open_positions = [pos for pos in all_positions if pos.fret > 0]
        
        if non_open_positions:
            violin_pos = random.choice(non_open_positions)
            
            # 创建对应的音符信息
            self.current_note = NoteInfo(
                pitch=violin_pos.pitch,
                note_name=violin_pos.note_name,
                octave=violin_pos.octave
            )
            
            # 在五线谱上显示音符
            self.staff_renderer.add_note(self.current_note)
            
            # 清除指板高亮
            self.violin_fingerboard.clear_highlight()
            
            # 更新信息显示
            self.update_info_display()
    
    def on_violin_position_click(self, position: ViolinPosition):
        """处理小提琴指板位置点击"""
        if not self.current_note:
            return
        
        # 检查答案
        if (position.note_name == self.current_note.note_name and 
            position.octave == self.current_note.octave):
            # 正确答案
            self.score += 1
            self.violin_fingerboard.highlight_position(position.string, position.fret)
            self.update_score_display()
            
            # 延迟生成下一题
            self.root.after(1000, self.next_note)
        else:
            # 错误答案
            self.score -= 1
            self.update_score_display()
    
    def update_info_display(self):
        """更新信息显示"""
        if self.current_note:
            info_text = f"当前音符: {self.current_note.note_name}{self.current_note.octave} ({self.current_note.solfege})"
            self.note_info_label.config(text=info_text)
        else:
            self.note_info_label.config(text="当前音符: 无")
    
    def update_score_display(self):
        """更新分数显示"""
        self.score_label.config(text=f"得分: {self.score}")
        
        # 根据分数改变颜色
        if self.score > 0:
            self.score_label.config(fg="green")
        elif self.score < 0:
            self.score_label.config(fg="red")
        else:
            self.score_label.config(fg="blue")
    
    def clear_all(self):
        """清除所有内容"""
        self.staff_renderer.clear_notes()
        self.violin_fingerboard.clear_highlight()
        self.current_note = None
        self.score = 0
        self.update_info_display()
        self.update_score_display()

def main():
    """主函数"""
    root = tk.Tk()
    app = ViolinPracticeApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
