# 小提琴练习程序 Makefile

.PHONY: help run test test-list test-one clean demo install check

# 默认目标
help:
	@echo "🎻 小提琴练习程序 - 可用命令:"
	@echo "================================"
	@echo "  make run          启动主程序（完整版）"
	@echo "  make run-violin    启动纯小提琴版本"
	@echo "  make demo          运行功能演示"
	@echo ""
	@echo "  make test          运行所有测试"
	@echo "  make test-list     列出所有测试"
	@echo "  make test-one T=<test_name>  运行特定测试"
	@echo ""
	@echo "  make check         检查项目状态"
	@echo "  make clean         清理临时文件"
	@echo "  make install       安装依赖（如果需要）"

# 运行主程序
run:
	@echo "🚀 启动小提琴练习程序（完整版）..."
	python staff_practice_app_final.py

# 运行纯小提琴版本
run-violin:
	@echo "🎻 启动纯小提琴版本..."
	python violin_practice_app.py

# 运行功能演示
demo:
	@echo "🎭 运行功能演示..."
	python demo_violin_features.py

# 运行所有测试
test:
	@echo "🧪 运行所有测试..."
	python run_tests.py

# 列出所有测试
test-list:
	@echo "📋 列出所有测试..."
	python run_tests.py -l

# 运行特定测试
test-one:
	@if [ -z "$(T)" ]; then \
		echo "❌ 请指定测试名称: make test-one T=test_violin_app"; \
		exit 1; \
	fi
	@echo "🧪 运行测试: $(T)"
	python run_tests.py -t $(T)

# 检查项目状态
check:
	@echo "📊 项目状态检查:"
	@echo "=================="
	@echo "📁 当前目录: $(PWD)"
	@echo "🐍 Python版本: $(shell python --version 2>/dev/null || echo '未安装')"
	@echo "📄 主程序文件:"
	@ls -la staff_practice_app_final.py violin_practice_app.py 2>/dev/null || echo "  文件不存在"
	@echo "🧪 测试文件数量: $(shell find test -name 'test_*.py' | wc -l)"
	@echo "📋 测试文件列表:"
	@find test -name 'test_*.py' -exec basename {} \; | sort | sed 's/^/  - /'

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	@find . -name "*.pyc" -delete 2>/dev/null || true
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyo" -delete 2>/dev/null || true
	@echo "✅ 清理完成"

# 安装依赖（如果需要）
install:
	@echo "📦 检查依赖..."
	@python -c "import tkinter; print('✅ tkinter 可用')" 2>/dev/null || \
		echo "❌ tkinter 不可用，请安装 python3-tk"
	@echo "✅ 所有依赖都已满足"

# 快速开始
quickstart: check demo
	@echo ""
	@echo "🎉 快速开始完成!"
	@echo "💡 现在可以使用以下命令:"
	@echo "  make run          # 启动完整版程序"
	@echo "  make run-violin   # 启动纯小提琴版本"
	@echo "  make test         # 运行所有测试"
