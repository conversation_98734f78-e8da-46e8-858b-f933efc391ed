#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文字体支持
"""

import os
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph
from reportlab.lib import colors

def test_fonts():
    """测试字体注册"""
    print("开始测试字体...")
    
    # 检查系统字体
    font_paths = [
        '/System/Library/Fonts/PingFang.ttc',
        '/System/Library/Fonts/Supplemental/Arial Unicode MS.ttf',
        '/Library/Fonts/Arial Unicode MS.ttf',
        '/System/Library/Fonts/STHeiti Light.ttc',
        '/System/Library/Fonts/STHeiti Medium.ttc',
        '/System/Library/Fonts/Hiragino Sans GB.ttc',
    ]
    
    available_fonts = []
    for font_path in font_paths:
        if os.path.exists(font_path):
            print(f"找到字体文件: {font_path}")
            available_fonts.append(font_path)
        else:
            print(f"字体文件不存在: {font_path}")
    
    # 尝试注册字体
    chinese_font = None
    for font_path in available_fonts:
        try:
            print(f"尝试注册字体: {font_path}")
            pdfmetrics.registerFont(TTFont('TestChineseFont', font_path))
            chinese_font = 'TestChineseFont'
            print(f"成功注册字体: {font_path}")
            break
        except Exception as e:
            print(f"注册字体失败 {font_path}: {e}")
    
    # 尝试CID字体
    if not chinese_font:
        try:
            from reportlab.pdfbase.cidfonts import UnicodeCIDFont
            pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
            chinese_font = 'STSong-Light'
            print("成功注册 STSong-Light CID字体")
        except Exception as e:
            print(f"注册CID字体失败: {e}")
    
    if not chinese_font:
        chinese_font = 'Helvetica'
        print("使用默认字体 Helvetica")
    
    # 创建测试PDF
    print(f"使用字体: {chinese_font}")
    create_test_pdf(chinese_font)

def create_test_pdf(font_name):
    """创建测试PDF"""
    doc = SimpleDocTemplate(
        "font_test.pdf",
        pagesize=A4,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=18
    )
    
    styles = getSampleStyleSheet()
    
    # 创建中文样式
    chinese_style = ParagraphStyle(
        name='ChineseTest',
        parent=styles['Normal'],
        fontName=font_name,
        fontSize=14,
        spaceAfter=12,
        leading=20
    )
    
    # 测试内容
    elements = []
    
    test_texts = [
        "English Text Test - This should display correctly",
        "中文测试 - 这应该正确显示",
        "华大智造深度研究报告",
        "数字测试: 123456789",
        "符号测试: ！@#￥%……&*（）",
        "混合测试: Hello 世界 123 ！",
    ]
    
    for text in test_texts:
        elements.append(Paragraph(text, chinese_style))
    
    try:
        doc.build(elements)
        print("测试PDF创建成功: font_test.pdf")
    except Exception as e:
        print(f"创建测试PDF失败: {e}")

if __name__ == "__main__":
    test_fonts()
