# MD转PDF工具 Makefile

.PHONY: help install test demo clean convert batch interactive

# 默认目标
help:
	@echo "🚀 MD转PDF工具使用指南"
	@echo "========================"
	@echo ""
	@echo "📦 安装和设置:"
	@echo "  make install     - 安装所有依赖"
	@echo "  make test        - 运行测试"
	@echo ""
	@echo "🔄 转换操作:"
	@echo "  make demo        - 转换演示文档"
	@echo "  make convert FILE=your_file.md  - 转换指定文件"
	@echo "  make batch       - 批量转换当前目录"
	@echo "  make interactive - 交互式转换"
	@echo ""
	@echo "🧹 清理操作:"
	@echo "  make clean       - 清理生成的PDF文件"
	@echo ""
	@echo "📖 查看文档:"
	@echo "  make docs        - 查看详细文档"

# 安装依赖
install:
	@echo "📦 安装MD转PDF工具依赖..."
	@if command -v brew >/dev/null 2>&1; then \
		echo "🍺 使用Homebrew安装wkhtmltopdf..."; \
		brew install wkhtmltopdf; \
	else \
		echo "❌ 未找到Homebrew，请手动安装wkhtmltopdf"; \
		echo "   macOS: brew install wkhtmltopdf"; \
		echo "   Ubuntu: sudo apt-get install wkhtmltopdf"; \
		exit 1; \
	fi
	@echo "🐍 安装Python依赖..."
	pip install -r requirements_pdf.txt
	@echo "✅ 安装完成!"

# 运行测试
test:
	@echo "🧪 运行MD转PDF工具测试..."
	python test_converter.py

# 转换演示文档
demo:
	@echo "📄 转换演示文档..."
	python quick_convert.py demo_document.md
	@if [ -f "demo_document.pdf" ]; then \
		echo "✅ 演示文档转换完成: demo_document.pdf"; \
		echo "📂 可以打开查看效果"; \
	fi

# 转换指定文件
convert:
	@if [ -z "$(FILE)" ]; then \
		echo "❌ 请指定文件: make convert FILE=your_file.md"; \
		exit 1; \
	fi
	@if [ ! -f "$(FILE)" ]; then \
		echo "❌ 文件不存在: $(FILE)"; \
		exit 1; \
	fi
	@echo "🔄 转换文件: $(FILE)"
	python quick_convert.py "$(FILE)"

# 批量转换
batch:
	@echo "📚 批量转换当前目录下的Markdown文件..."
	python batch_md_to_pdf.py . --exclude README_PDF_CONVERTER.md

# 交互式转换
interactive:
	@echo "🤖 启动交互式转换模式..."
	python quick_convert.py --interactive

# 清理生成的PDF文件
clean:
	@echo "🧹 清理生成的PDF文件..."
	@find . -name "*.pdf" -type f -exec echo "删除: {}" \; -delete
	@echo "✅ 清理完成"

# 查看详细文档
docs:
	@if command -v cat >/dev/null 2>&1; then \
		echo "📖 MD转PDF工具详细文档:"; \
		echo ""; \
		cat README_PDF_CONVERTER.md; \
	else \
		echo "📖 请查看文件: README_PDF_CONVERTER.md"; \
	fi

# 检查依赖状态
check:
	@echo "🔍 检查依赖状态..."
	@echo "Python版本:"
	@python --version
	@echo ""
	@echo "检查wkhtmltopdf:"
	@if command -v wkhtmltopdf >/dev/null 2>&1; then \
		echo "✅ wkhtmltopdf 已安装"; \
		wkhtmltopdf --version | head -1; \
	else \
		echo "❌ wkhtmltopdf 未安装"; \
	fi
	@echo ""
	@echo "检查Python包:"
	@python -c "import markdown; print('✅ markdown 已安装')" 2>/dev/null || echo "❌ markdown 未安装"
	@python -c "import pdfkit; print('✅ pdfkit 已安装')" 2>/dev/null || echo "❌ pdfkit 未安装"

# 显示项目状态
status:
	@echo "📊 项目状态:"
	@echo "============"
	@echo "📁 当前目录: $(PWD)"
	@echo "📄 Markdown文件数量: $(shell find . -name "*.md" | wc -l)"
	@echo "📋 PDF文件数量: $(shell find . -name "*.pdf" | wc -l)"
	@echo ""
	@echo "📄 Markdown文件列表:"
	@find . -name "*.md" -exec echo "  - {}" \;
	@echo ""
	@echo "📋 PDF文件列表:"
	@find . -name "*.pdf" -exec echo "  - {}" \; || echo "  (无PDF文件)"

# 快速开始
quickstart: install demo
	@echo ""
	@echo "🎉 快速开始完成!"
	@echo "📄 演示文档已转换为PDF"
	@echo "🔧 现在可以使用以下命令:"
	@echo "  make convert FILE=your_file.md  # 转换指定文件"
	@echo "  make batch                      # 批量转换"
	@echo "  make interactive                # 交互式转换"
