#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速MD转PDF工具
简化版的转换工具，用于快速转换单个文件
"""

import os
import sys
import argparse
from pathlib import Path


def quick_convert(md_file, output_file=None):
    """快速转换单个Markdown文件为PDF"""
    
    # 检查输入文件
    if not os.path.exists(md_file):
        print(f"❌ 文件不存在: {md_file}")
        return False
    
    # 确定输出文件名
    if not output_file:
        output_file = str(Path(md_file).with_suffix('.pdf'))
    
    print(f"🔄 开始转换: {md_file}")
    print(f"📄 输出文件: {output_file}")
    
    try:
        # 导入转换器
        from md_to_pdf import MarkdownToPDFConverter
        
        # 创建转换器实例
        converter = MarkdownToPDFConverter()
        
        # 执行转换
        result_file = converter.convert(md_file, output_file)
        
        # 检查结果
        if os.path.exists(result_file):
            file_size = os.path.getsize(result_file)
            print(f"✅ 转换成功!")
            print(f"📊 文件大小: {file_size:,} 字节")
            print(f"📁 保存位置: {os.path.abspath(result_file)}")
            return True
        else:
            print("❌ 转换失败: 输出文件未生成")
            return False
            
    except ImportError as e:
        print("❌ 缺少依赖包，请运行:")
        print("   pip install -r requirements_pdf.txt")
        print("   brew install wkhtmltopdf  # macOS")
        return False
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        return False


def list_markdown_files():
    """列出当前目录下的Markdown文件"""
    import glob
    
    patterns = ['*.md', '*.markdown']
    files = []
    
    for pattern in patterns:
        files.extend(glob.glob(pattern))
    
    return sorted(files)


def interactive_mode():
    """交互式模式"""
    print("🤖 MD转PDF交互式转换工具")
    print("=" * 40)
    
    # 列出可用的Markdown文件
    md_files = list_markdown_files()
    
    if not md_files:
        print("❌ 当前目录下没有找到Markdown文件")
        return False
    
    print("📄 找到以下Markdown文件:")
    for i, file in enumerate(md_files, 1):
        file_size = os.path.getsize(file)
        print(f"  {i}. {file} ({file_size:,} 字节)")
    
    print("  0. 退出")
    print()
    
    while True:
        try:
            choice = input("请选择要转换的文件编号 (0退出): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                return True
            
            file_index = int(choice) - 1
            
            if 0 <= file_index < len(md_files):
                selected_file = md_files[file_index]
                print(f"\n🎯 选择了: {selected_file}")
                
                # 询问输出文件名
                default_output = str(Path(selected_file).with_suffix('.pdf'))
                output_file = input(f"输出文件名 (默认: {default_output}): ").strip()
                
                if not output_file:
                    output_file = default_output
                
                # 执行转换
                print()
                success = quick_convert(selected_file, output_file)
                
                if success:
                    # 询问是否继续
                    continue_choice = input("\n是否继续转换其他文件? (y/n): ").strip().lower()
                    if continue_choice not in ['y', 'yes', '是']:
                        print("👋 转换完成!")
                        return True
                    print()
                else:
                    return False
            else:
                print("❌ 无效的选择，请重新输入")
                
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='快速MD转PDF工具')
    parser.add_argument('file', nargs='?', help='要转换的Markdown文件')
    parser.add_argument('-o', '--output', help='输出PDF文件名')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式模式')
    
    args = parser.parse_args()
    
    if args.interactive or not args.file:
        # 交互式模式
        return interactive_mode()
    else:
        # 命令行模式
        return quick_convert(args.file, args.output)


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
        sys.exit(0)
